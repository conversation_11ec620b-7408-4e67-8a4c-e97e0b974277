#!/usr/bin/env python3
"""
Demonstration of the Enhanced RDF Query Agent.
Shows the improvements over the original hardcoded approach.
"""
import asyncio
import sys
from pathlib import Path

# Add the backend directory to the Python path
sys.path.insert(0, str(Path(__file__).parent))

from agents.rdf_query_agent import RDFQueryAgent
from agents.ttl_analyzer import TTLAnalyzer


class MockDependencies:
    """Mock dependencies for demonstration."""
    def __init__(self):
        self.settings = MockSettings()
        self.graphdb_client = None


class MockSettings:
    """Mock settings for demonstration."""
    def __init__(self):
        self.ai = MockAISettings()


class MockAISettings:
    """Mock AI settings for demonstration."""
    def __init__(self):
        self.or_api_key = "demo-key"


def print_section(title: str):
    """Print a formatted section header."""
    print(f"\n{'='*60}")
    print(f"🔥 {title}")
    print(f"{'='*60}")


def print_subsection(title: str):
    """Print a formatted subsection header."""
    print(f"\n{'─'*40}")
    print(f"📋 {title}")
    print(f"{'─'*40}")


async def demonstrate_old_vs_new_approach():
    """Demonstrate the differences between old and new approaches."""
    print_section("OLD vs NEW APPROACH COMPARISON")
    
    print("❌ OLD APPROACH PROBLEMS:")
    print("   • Hardcoded namespaces (building:, address:, schema:, geo:)")
    print("   • No dynamic TTL file analysis")
    print("   • Basic string-based SPARQL validation")
    print("   • Fixed system prompt that doesn't adapt to data")
    print("   • No understanding of actual data structure")
    print("   • Cannot work with arbitrary TTL files")
    
    print("\n✅ NEW APPROACH IMPROVEMENTS:")
    print("   • Dynamic TTL analysis extracts actual prefixes and structure")
    print("   • Proper SPARQL syntax validation using rdflib")
    print("   • Context-aware query generation based on available data")
    print("   • Intelligent strategy development for different question types")
    print("   • Works with any TTL file and vocabulary")
    print("   • Provides rich context about data availability")


async def demonstrate_ttl_analysis():
    """Demonstrate the comprehensive TTL analysis."""
    print_section("COMPREHENSIVE TTL ANALYSIS")
    
    analyzer = TTLAnalyzer()
    
    # Analyze example.ttl
    example_path = Path("test_data/example.ttl")
    if example_path.exists():
        print_subsection("Example.ttl Analysis")
        analysis = analyzer.analyze_ttl_file(str(example_path))
        
        print(f"📊 Basic Statistics:")
        print(f"   • Total triples: {analysis.total_triples:,}")
        print(f"   • Classes: {len(analysis.classes)}")
        print(f"   • Properties: {len(analysis.properties)}")
        print(f"   • Prefixes: {len(analysis.prefixes)}")
        
        print(f"\n🏷️ Discovered Classes:")
        for class_name, count in analysis.class_counts.items():
            print(f"   • {class_name}: {count} instances")
        
        print(f"\n🔗 Properties by Namespace:")
        for namespace, props in analysis.property_by_namespace.items():
            print(f"   • {namespace}: {len(props)} properties")
            if props:
                print(f"     Examples: {', '.join(props[:3])}")
        
        print(f"\n📋 Sample Data Values:")
        for prop, values in list(analysis.sample_data.items())[:5]:
            print(f"   • {prop}: {', '.join(values)}")
    
    # Analyze hobbit.ttl
    hobbit_path = Path("test_data/hobbit.ttl")
    if hobbit_path.exists():
        print_subsection("Hobbit.ttl Analysis")
        analysis = analyzer.analyze_ttl_file(str(hobbit_path))
        
        print(f"📊 Basic Statistics:")
        print(f"   • Total triples: {analysis.total_triples:,}")
        print(f"   • Classes: {len(analysis.classes)}")
        print(f"   • Properties: {len(analysis.properties)}")
        
        print(f"\n🏷️ Top Classes:")
        sorted_classes = sorted(analysis.class_counts.items(), key=lambda x: x[1], reverse=True)
        for class_name, count in sorted_classes[:5]:
            print(f"   • {class_name}: {count} instances")
        
        print(f"\n🎯 Domain-Specific Insights:")
        benchmark_classes = [cls for cls in analysis.class_counts.keys() if 'benchmark' in cls.lower()]
        kpi_classes = [cls for cls in analysis.class_counts.keys() if 'kpi' in cls.lower()]
        if benchmark_classes:
            print(f"   • Benchmark classes: {benchmark_classes}")
        if kpi_classes:
            print(f"   • KPI classes: {kpi_classes}")


async def demonstrate_sparql_validation():
    """Demonstrate SPARQL validation improvements."""
    print_section("SPARQL VALIDATION IMPROVEMENTS")
    
    analyzer = TTLAnalyzer()
    
    print("✅ VALID QUERIES:")
    valid_queries = [
        ("Address Count Query", """
        PREFIX ibpdi: <https://ibpdi.datacat.org/class/>
        SELECT (COUNT(*) as ?count) WHERE {
            ?address a ibpdi:Address .
        }
        """),
        ("City List Query", """
        PREFIX ibpdi: <https://ibpdi.datacat.org/class/>
        PREFIX prop: <https://ibpdi.datacat.org/property/>
        SELECT DISTINCT ?city WHERE {
            ?address a ibpdi:Address ;
                     prop:city ?city .
        }
        """),
        ("Benchmark Existence Query", """
        PREFIX hobbit: <http://w3id.org/hobbit/vocab#>
        ASK WHERE {
            ?benchmark a hobbit:Benchmark .
        }
        """)
    ]
    
    for name, query in valid_queries:
        is_valid, query_type, error = analyzer.validate_sparql_query(query.strip())
        status = "✅" if is_valid else "❌"
        print(f"   {status} {name}: {query_type}")
    
    print("\n❌ INVALID QUERIES:")
    invalid_queries = [
        ("Syntax Error", "INVALID SPARQL QUERY"),
        ("Missing Brace", "SELECT ?x WHERE { ?x ?y ?z"),
        ("Typo in Keyword", "SELCT ?x WHERE { ?x ?y ?z }")
    ]
    
    for name, query in invalid_queries:
        is_valid, query_type, error = analyzer.validate_sparql_query(query)
        status = "✅" if not is_valid else "❌"
        print(f"   {status} {name}: Correctly identified as invalid")
        if error:
            print(f"      Error: {error[:80]}...")


async def demonstrate_agent_capabilities():
    """Demonstrate the enhanced agent capabilities."""
    print_section("ENHANCED AGENT CAPABILITIES")
    
    deps = MockDependencies()
    agent = RDFQueryAgent(deps)
    
    print_subsection("Without Data Loaded")
    capabilities = await agent.get_capabilities()
    print(f"   • TTL loaded: {capabilities.ttl_loaded}")
    print(f"   • Total triples: {capabilities.total_triples}")
    print(f"   • Sample queries: {capabilities.sample_queries}")
    
    # Load example data
    example_path = Path("test_data/example.ttl")
    if example_path.exists():
        print_subsection("Loading Example Data")
        response = await agent.load_ttl_file(str(example_path))
        print(f"   • Success: {response.success}")
        print(f"   • Message: {response.message}")
        print(f"   • Summary: {response.analysis_summary}")
        
        print_subsection("Capabilities with Data")
        capabilities = await agent.get_capabilities()
        print(f"   • TTL loaded: {capabilities.ttl_loaded}")
        print(f"   • Total triples: {capabilities.total_triples:,}")
        print(f"   • Available classes: {len(capabilities.available_classes)}")
        print(f"   • Classes: {capabilities.available_classes}")
        print(f"   • Data domains: {capabilities.data_domains}")
        
        print_subsection("Data Exploration")
        exploration = await agent.explore_data()
        print(f"   • Summary: {exploration.summary}")
        print(f"   • Key entities: {exploration.key_entities}")
        print(f"   • Interesting patterns: {exploration.interesting_patterns}")
        print(f"   • Suggested questions: {len(exploration.suggested_questions)}")
        
        print("\n   📝 Sample Suggested Questions:")
        for i, suggestion in enumerate(exploration.suggested_questions[:3], 1):
            print(f"      {i}. {suggestion.question}")
            print(f"         → {suggestion.description}")


async def demonstrate_context_generation():
    """Demonstrate the rich context generation for SPARQL queries."""
    print_section("CONTEXT-AWARE QUERY GENERATION")
    
    analyzer = TTLAnalyzer()
    example_path = Path("test_data/example.ttl")
    
    if not example_path.exists():
        print("⚠️ Example file not found")
        return
    
    analysis = analyzer.analyze_ttl_file(str(example_path))
    
    sample_questions = [
        ("How many addresses are there?", "count"),
        ("What cities are in the dataset?", "list"),
        ("Show me examples of buildings", "examples"),
        ("What countries are represented?", "distinct_values")
    ]
    
    print("🎯 Context Generated for Different Question Types:")
    
    for question, intent in sample_questions:
        print(f"\n❓ Question: '{question}'")
        print(f"🎯 Intent: {intent}")
        print(f"📊 Context Provided:")
        print(f"   • Total entities: {sum(analysis.class_counts.values())}")
        print(f"   • Available classes: {list(analysis.class_counts.keys())}")
        
        # Show relevant properties
        relevant_props = []
        question_lower = question.lower()
        for prop in analysis.sample_data.keys():
            prop_name = prop.split(':')[-1].lower()
            if any(word in prop_name for word in question_lower.split()):
                relevant_props.append(prop)
        
        if relevant_props:
            print(f"   • Relevant properties: {relevant_props}")
            for prop in relevant_props[:2]:
                print(f"     {prop}: {analysis.sample_data[prop]}")
        
        # Show required prefixes
        needed_prefixes = set()
        for class_name in analysis.class_counts.keys():
            prefix = class_name.split(':')[0]
            needed_prefixes.add(prefix)
        for prop in relevant_props:
            prefix = prop.split(':')[0]
            needed_prefixes.add(prefix)
        
        print(f"   • Required prefixes: {list(needed_prefixes)}")
        
        # Show strategy
        if intent == "count":
            print(f"   • Strategy: Generate COUNT queries for relevant classes")
        elif intent == "list":
            print(f"   • Strategy: Use SELECT DISTINCT for relevant properties")
        elif intent == "examples":
            print(f"   • Strategy: Use SELECT with LIMIT for sample instances")


async def main():
    """Run the complete demonstration."""
    print("🚀 ENHANCED RDF QUERY AGENT DEMONSTRATION")
    print("This demo shows the improvements made to the RDF Query Agent")
    print("to work generically with any TTL file instead of hardcoded schemas.")
    
    await demonstrate_old_vs_new_approach()
    await demonstrate_ttl_analysis()
    await demonstrate_sparql_validation()
    await demonstrate_agent_capabilities()
    await demonstrate_context_generation()
    
    print_section("SUMMARY OF IMPROVEMENTS")
    print("✅ COMPLETED ENHANCEMENTS:")
    print("   1. ✅ Dynamic TTL file analysis")
    print("   2. ✅ Proper SPARQL syntax validation")
    print("   3. ✅ Context-aware query generation")
    print("   4. ✅ Intelligent strategy development")
    print("   5. ✅ Generic vocabulary support")
    print("   6. ✅ Rich data exploration capabilities")
    print("   7. ✅ Comprehensive test suite")
    
    print("\n🎯 KEY BENEFITS:")
    print("   • Works with ANY TTL file and vocabulary")
    print("   • Provides intelligent context for query generation")
    print("   • Validates SPARQL queries properly")
    print("   • Adapts to the actual data structure")
    print("   • Offers meaningful insights about the data")
    print("   • Suggests relevant questions users can ask")
    
    print("\n🚀 The enhanced RDF Query Agent is now ready for production use!")


if __name__ == "__main__":
    asyncio.run(main())
