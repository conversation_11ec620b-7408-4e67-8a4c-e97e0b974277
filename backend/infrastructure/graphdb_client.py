"""
GraphDB client for RDF data storage and SPARQL queries.
Uses generic triple store interface with RDFLib for portability.
"""
import logging
from typing import Dict, List, Optional, Any

from config.settings import GraphDBSettings
from .triple_store_interface import TripleStoreConfig
from .rdflib_triple_store import RDFLibTripleStore

logger = logging.getLogger(__name__)


class GraphDBClient:
    """
    GraphDB client that uses the generic triple store interface.
    Maintains backward compatibility with the existing API.
    """
    
    def __init__(self, settings: GraphDBSettings):
        self.settings = settings
        
        # Create triple store configuration
        query_endpoint = f"{settings.url}/repositories/{settings.default_repository}"
        update_endpoint = f"{settings.url}/repositories/{settings.default_repository}/statements"
        
        config = TripleStoreConfig(
            query_endpoint=query_endpoint,
            update_endpoint=update_endpoint,
            default_repository=settings.default_repository,
            timeout=30
        )
        
        # Initialize the generic triple store implementation
        self._store = RDFLibTripleStore(config)
        
        # For backward compatibility
        self.base_url = settings.url
        self.repository = settings.default_repository
    
    async def initialize(self):
        """Initialize the GraphDB client."""
        try:
            await self._store.initialize()
            logger.info("GraphDB client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize GraphDB client: {e}")
            raise
    
    async def close(self):
        """Close the GraphDB client."""
        await self._store.close()
        logger.info("GraphDB client closed")
    
    async def health_check(self) -> bool:
        """Check if GraphDB is accessible."""
        return await self._store.health_check()
    
    async def execute_sparql_query(self, query: str, repository: Optional[str] = None) -> Dict[str, Any]:
        """
        Execute a SPARQL query and return results in the original format.
        Maintains backward compatibility with the existing API.
        """
        result = await self._store.execute_sparql_query(query, repository)
        
        if not result.success:
            raise Exception(f"SPARQL query failed: {result.error_message}")
        
        # Convert to the original format expected by existing code
        if query.strip().upper().startswith('ASK'):
            # ASK query - return boolean result
            ask_result = result.results[0].get('result', False) if result.results else False
            return {
                "head": {},
                "boolean": ask_result
            }
        elif query.strip().upper().startswith(('CONSTRUCT', 'DESCRIBE')):
            # CONSTRUCT/DESCRIBE query - return triples
            return {
                "results": {
                    "bindings": [
                        {
                            "subject": {"type": "uri", "value": r["subject"]},
                            "predicate": {"type": "uri", "value": r["predicate"]},
                            "object": {"type": "uri", "value": r["object"]}
                        }
                        for r in result.results
                    ]
                }
            }
        else:
            # SELECT query - convert to original binding format
            bindings = []
            for row in result.results:
                binding = {}
                for var, value in row.items():
                    if value is not None:
                        # Determine the type of the value
                        if value.startswith('http://') or value.startswith('https://'):
                            binding[var] = {"type": "uri", "value": value}
                        elif value.startswith('_:'):
                            binding[var] = {"type": "bnode", "value": value}
                        else:
                            binding[var] = {"type": "literal", "value": value}
                bindings.append(binding)
            
            return {
                "head": {"vars": result.variables},
                "results": {"bindings": bindings}
            }
    
    async def execute_sparql_update(self, update: str, repository: Optional[str] = None) -> bool:
        """Execute a SPARQL update operation."""
        result = await self._store.execute_sparql_update(update, repository)
        
        if result.success:
            logger.debug("SPARQL update executed successfully")
            return True
        else:
            logger.error(f"SPARQL update failed: {result.error_message}")
            return False
    
    async def upload_ttl_data(self, ttl_content: str, repository: Optional[str] = None, 
                             context: Optional[str] = None) -> bool:
        """Upload TTL data to GraphDB."""
        result = await self._store.upload_ttl_data(ttl_content, repository, context)
        
        if result.success:
            logger.info("TTL data uploaded successfully")
            return True
        else:
            logger.error(f"TTL upload failed: {result.error_message}")
            return False
    
    async def get_repositories(self) -> List[Dict[str, Any]]:
        """Get list of available repositories."""
        return await self._store.get_repositories()
    
    async def clear_repository(self, repository: Optional[str] = None) -> bool:
        """Clear all data from a repository."""
        result = await self._store.clear_repository(repository)
        
        if result.success:
            repo_name = repository or self.repository
            logger.info(f"Repository {repo_name} cleared successfully")
            return True
        else:
            logger.error(f"Failed to clear repository: {result.error_message}")
            return False
    
    async def get_repository_size(self, repository: Optional[str] = None) -> int:
        """Get the number of statements in a repository."""
        return await self._store.get_repository_size(repository)
    
    # Additional helper methods for backward compatibility
    @property
    def session(self):
        """For backward compatibility - returns the internal session."""
        return self._store.session
    
    def _get_repository(self, repository: Optional[str] = None) -> str:
        """Get the repository name to use."""
        return repository or self.repository
