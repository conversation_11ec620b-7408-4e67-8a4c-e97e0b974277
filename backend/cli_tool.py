#!/usr/bin/env python3
"""
RDF Agent CLI Tool
A comprehensive command-line interface for querying use cases through the RDF Agent API.
Provides an interactive TUI for selecting use cases and asking questions.
"""

import asyncio
import json
import os
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

# Rich imports for beautiful CLI
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.prompt import Prompt, Confirm, IntPrompt
from rich.text import Text
from rich.table import Table
from rich.markdown import Markdown
from rich import box
from rich.align import Align
from rich.syntax import Syntax
from rich.live import Live
from rich.layout import Layout
from rich.columns import Columns

# HTTP client
import httpx
import typer

# Load environment
import dotenv
dotenv.load_dotenv()

# Configuration
API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8001")
DEFAULT_TIMEOUT = 30.0

console = Console()
app = typer.Typer(help="RDF Agent CLI Tool - Interactive use case querying")


@dataclass
class UseCase:
    """Represents a use case with its metadata."""
    name: str
    repository: str
    description: Optional[str] = None
    triple_count: Optional[int] = None
    last_updated: Optional[str] = None
    status: str = "unknown"


@dataclass
class QuerySession:
    """Represents an active query session."""
    session_id: str
    use_case: UseCase
    created_at: str
    turn_count: int = 0


class APIClient:
    """HTTP client for the RDF Agent API."""
    
    def __init__(self, base_url: str = API_BASE_URL, timeout: float = DEFAULT_TIMEOUT):
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.client = httpx.AsyncClient(timeout=timeout)
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    async def health_check(self) -> bool:
        """Check if the API is healthy."""
        try:
            response = await self.client.get(f"{self.base_url}/health")
            return response.status_code == 200
        except Exception:
            return False
    
    async def get_repositories(self) -> List[Dict[str, Any]]:
        """Get list of available repositories (use cases) from GraphDB."""
        try:
            response = await self.client.get(f"{self.base_url}/api/v1/admin/repositories")
            response.raise_for_status()
            data = response.json()

            # Extract repository names from the response
            repositories = data.get("repositories", [])

            # Handle different response formats
            if isinstance(repositories, list):
                # If it's a list of strings, return as is
                if repositories and isinstance(repositories[0], str):
                    return repositories
                # If it's a list of objects, extract names
                elif repositories and isinstance(repositories[0], dict):
                    return [repo.get("id", repo.get("name", str(repo))) for repo in repositories]

            return repositories if isinstance(repositories, list) else []

        except Exception as e:
            console.print(f"[red]Error fetching repositories: {e}[/red]")
            return []
    
    async def get_repository_size(self, repository: str) -> Optional[int]:
        """Get the size of a repository in triples from GraphDB."""
        try:
            response = await self.client.get(f"{self.base_url}/api/v1/admin/repositories/{repository}/size")
            response.raise_for_status()
            data = response.json()
            return data.get("size", 0)
        except Exception as e:
            console.print(f"[dim]Could not get size for repository {repository}: {e}[/dim]")
            return None
    
    async def create_session(self, metadata: Optional[Dict[str, Any]] = None) -> Optional[str]:
        """Create a new query session."""
        try:
            payload = {"metadata": metadata or {}}
            response = await self.client.post(f"{self.base_url}/api/v1/sessions/", json=payload)
            response.raise_for_status()
            data = response.json()
            return data.get("session_id")
        except Exception as e:
            console.print(f"[red]Error creating session: {e}[/red]")
            return None
    
    async def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session details."""
        try:
            response = await self.client.get(f"{self.base_url}/api/v1/sessions/{session_id}")
            response.raise_for_status()
            return response.json()
        except Exception:
            return None
    
    async def query(self, query_text: str, session_id: Optional[str] = None,
                   query_type: str = "natural_language", repository: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Execute a query against a specific repository."""
        try:
            payload = {
                "query_text": query_text,
                "query_type": query_type,
                "session_id": session_id,
                "context": {
                    "repository": repository
                } if repository else {}
            }
            response = await self.client.post(f"{self.base_url}/api/v1/query/", json=payload)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            console.print(f"[red]Error executing query: {e}[/red]")
            return None
    
    async def get_query_examples(self) -> Dict[str, Any]:
        """Get example queries."""
        try:
            response = await self.client.get(f"{self.base_url}/api/v1/query/examples")
            response.raise_for_status()
            return response.json()
        except Exception:
            return {"natural_language_examples": [], "sparql_examples": []}
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Get system status."""
        try:
            response = await self.client.get(f"{self.base_url}/api/v1/admin/status")
            response.raise_for_status()
            return response.json()
        except Exception:
            return {"status": "unknown", "services": {}}


class RDFAgentCLI:
    """Main CLI application class."""
    
    def __init__(self):
        self.console = Console()
        self.api_client: Optional[APIClient] = None
        self.current_session: Optional[QuerySession] = None
        self.use_cases: List[UseCase] = []
    
    def print_header(self):
        """Print application header."""
        header_text = """
🚀 RDF Agent CLI Tool
Interactive Use Case Querying System
        """
        
        header_panel = Panel(
            Align.center(header_text),
            border_style="bright_blue",
            box=box.DOUBLE_EDGE,
            padding=(1, 2)
        )
        
        self.console.print(header_panel)
        self.console.print()
    
    def print_status(self):
        """Print current system status."""
        status_table = Table(show_header=False, box=box.MINIMAL)
        status_table.add_column("Item", style="cyan")
        status_table.add_column("Value", style="green")
        
        if self.current_session:
            status_table.add_row("🎯 Active Use Case", self.current_session.use_case.name)
            status_table.add_row("📊 Repository", self.current_session.use_case.repository)
            status_table.add_row("🔢 Session ID", self.current_session.session_id[:8] + "...")
            status_table.add_row("💬 Conversation Turns", str(self.current_session.turn_count))
            if self.current_session.use_case.triple_count:
                status_table.add_row("📈 Triple Count", f"{self.current_session.use_case.triple_count:,}")
        else:
            status_table.add_row("🎯 Active Use Case", "[yellow]None selected[/yellow]")
            status_table.add_row("📊 Available Use Cases", str(len(self.use_cases)))
        
        status_table.add_row("🌐 API Endpoint", API_BASE_URL)
        
        status_panel = Panel(
            status_table,
            title="📊 System Status",
            border_style="green" if self.current_session else "yellow",
            padding=(0, 1)
        )
        
        self.console.print(status_panel)
    
    async def check_api_connection(self) -> bool:
        """Check API connection and display status."""
        self.console.print("🔍 Checking API connection...")
        
        async with APIClient() as client:
            self.api_client = client
            
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=self.console,
                transient=True
            ) as progress:
                task = progress.add_task("Connecting to API...", total=None)
                
                is_healthy = await client.health_check()
                
                if is_healthy:
                    progress.update(task, description="✅ API connection successful!")
                    self.console.print("[green]✅ Connected to RDF Agent API[/green]")
                    return True
                else:
                    progress.update(task, description="❌ API connection failed!")
                    self.console.print(f"[red]❌ Cannot connect to API at {API_BASE_URL}[/red]")
                    self.console.print("[yellow]Please ensure the API server is running[/yellow]")
                    return False
    
    async def load_use_cases(self) -> bool:
        """Load available use cases from the API."""
        if not self.api_client:
            return False
        
        self.console.print("📚 Loading available use cases...")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console,
            transient=True
        ) as progress:
            task = progress.add_task("Fetching repositories...", total=None)
            
            repositories = await self.api_client.get_repositories()
            
            if not repositories:
                progress.update(task, description="⚠️ No repositories found")
                self.console.print("[yellow]⚠️ No use cases found[/yellow]")
                return False
            
            progress.update(task, description="Loading repository details...")
            
            self.use_cases = []
            for repo in repositories:
                # Get repository size from GraphDB
                size = await self.api_client.get_repository_size(repo)

                # Create use case representing a GraphDB repository
                use_case = UseCase(
                    name=repo,
                    repository=repo,
                    description=f"GraphDB repository containing RDF data for use case: {repo}",
                    triple_count=size,
                    status="available" if size is not None else "unknown"
                )
                self.use_cases.append(use_case)
            
            progress.update(task, description=f"✅ Loaded {len(self.use_cases)} use cases")
        
        self.console.print(f"[green]✅ Found {len(self.use_cases)} available use cases[/green]")
        return True
    
    def display_use_cases(self):
        """Display available use cases in a table."""
        if not self.use_cases:
            self.console.print("[yellow]No use cases available[/yellow]")
            return
        
        table = Table(show_header=True, header_style="bold magenta", box=box.ROUNDED)
        table.add_column("#", style="dim", width=3)
        table.add_column("Use Case", style="cyan", min_width=20)
        table.add_column("Repository", style="blue", min_width=15)
        table.add_column("Triples", style="green", justify="right")
        table.add_column("Status", style="yellow")
        
        for i, use_case in enumerate(self.use_cases, 1):
            triple_count = f"{use_case.triple_count:,}" if use_case.triple_count else "Unknown"
            status_style = "green" if use_case.status == "available" else "yellow"
            
            table.add_row(
                str(i),
                use_case.name,
                use_case.repository,
                triple_count,
                f"[{status_style}]{use_case.status}[/{status_style}]"
            )
        
        panel = Panel(
            table,
            title="🎯 Available Use Cases",
            border_style="blue",
            padding=(1, 2)
        )
        
        self.console.print(panel)
    
    async def select_use_case(self) -> bool:
        """Allow user to select a use case."""
        if not self.use_cases:
            self.console.print("[red]No use cases available. Please check API connection.[/red]")
            return False
        
        self.display_use_cases()
        
        try:
            choice = IntPrompt.ask(
                "\n[cyan]Select a use case (number)[/cyan]",
                default=1,
                show_default=True,
                console=self.console
            )
            
            if 1 <= choice <= len(self.use_cases):
                selected_use_case = self.use_cases[choice - 1]
                
                # Create a new session for this GraphDB repository
                session_id = await self.api_client.create_session({
                    "use_case": selected_use_case.name,
                    "repository": selected_use_case.repository,
                    "repository_type": "graphdb",
                    "triple_count": selected_use_case.triple_count,
                    "selected_at": datetime.now().isoformat()
                })
                
                if session_id:
                    self.current_session = QuerySession(
                        session_id=session_id,
                        use_case=selected_use_case,
                        created_at=datetime.now().isoformat()
                    )
                    
                    self.console.print(f"[green]✅ Selected use case: {selected_use_case.name}[/green]")
                    self.console.print(f"[dim]Session ID: {session_id}[/dim]")
                    return True
                else:
                    self.console.print("[red]❌ Failed to create session[/red]")
                    return False
            else:
                self.console.print("[red]Invalid selection[/red]")
                return False
                
        except (ValueError, KeyboardInterrupt):
            self.console.print("[yellow]Selection cancelled[/yellow]")
            return False

    async def display_query_examples(self):
        """Display example queries for the current use case."""
        if not self.api_client:
            return

        examples = await self.api_client.get_query_examples()

        # Natural language examples
        nl_examples = examples.get("natural_language_examples", [])
        if nl_examples:
            nl_table = Table(show_header=True, header_style="bold cyan", box=box.MINIMAL)
            nl_table.add_column("#", style="dim", width=3)
            nl_table.add_column("Natural Language Query", style="white")

            for i, example in enumerate(nl_examples[:5], 1):
                nl_table.add_row(str(i), example)

            self.console.print(Panel(
                nl_table,
                title="💬 Example Natural Language Queries",
                border_style="cyan",
                padding=(0, 1)
            ))

        # SPARQL examples
        sparql_examples = examples.get("sparql_examples", [])
        if sparql_examples:
            for i, example in enumerate(sparql_examples[:2], 1):
                query_syntax = Syntax(
                    example.get("query", "").strip(),
                    "sparql",
                    theme="monokai",
                    line_numbers=False,
                    word_wrap=True
                )

                self.console.print(Panel(
                    Text(example.get("description", "SPARQL Query")) + "\n\n" + query_syntax,
                    title=f"🔍 SPARQL Example {i}",
                    border_style="blue",
                    padding=(0, 1)
                ))

    def format_query_response(self, response: Dict[str, Any]) -> None:
        """Format and display query response."""
        if not response:
            self.console.print("[red]❌ No response received[/red]")
            return

        # Response header
        response_info = f"""**Query ID:** {response.get('query_id', 'Unknown')}
**Processing Time:** {response.get('processing_time', 0):.2f}s
**Confidence:** {response.get('confidence', 0):.1%} """ if response.get('confidence') else ""

        self.console.print(Panel(
            Markdown(response_info),
            title="📊 Query Response",
            border_style="green",
            padding=(0, 1)
        ))

        # Main response
        response_text = response.get('response_text', 'No response text')
        self.console.print(Panel(
            response_text,
            title="💬 Answer",
            border_style="blue",
            padding=(1, 2)
        ))

        # SPARQL query if available
        sparql_query = response.get('sparql_query')
        if sparql_query:
            query_syntax = Syntax(
                sparql_query,
                "sparql",
                theme="monokai",
                line_numbers=True,
                word_wrap=True
            )

            self.console.print(Panel(
                query_syntax,
                title="🔍 Generated SPARQL Query",
                border_style="cyan",
                padding=(0, 1)
            ))

        # Results if available
        results = response.get('results')
        if results and len(results) > 0:
            self.display_query_results(results)

        # Sources if available
        sources = response.get('sources', [])
        if sources:
            sources_text = "\n".join([f"• {source}" for source in sources])
            self.console.print(Panel(
                sources_text,
                title="📚 Sources",
                border_style="dim",
                padding=(0, 1)
            ))

    def display_query_results(self, results: List[Dict[str, Any]]):
        """Display query results in a table."""
        if not results:
            return

        # Create table with dynamic columns
        table = Table(show_header=True, header_style="bold magenta", box=box.ROUNDED)

        # Get all unique keys from results
        all_keys = set()
        for result in results:
            all_keys.update(result.keys())

        # Add columns
        for key in sorted(all_keys):
            table.add_column(key, overflow="fold", max_width=30)

        # Add rows (limit to first 10 for display)
        display_count = min(10, len(results))
        for result in results[:display_count]:
            row_values = []
            for key in sorted(all_keys):
                value = result.get(key, "")
                # Truncate long values
                str_value = str(value)
                if len(str_value) > 50:
                    str_value = str_value[:47] + "..."
                row_values.append(str_value)
            table.add_row(*row_values)

        if len(results) > display_count:
            # Add a row indicating more results
            table.add_row(*[f"... (+{len(results) - display_count} more)" if i == 0 else ""
                           for i in range(len(all_keys))])

        self.console.print(Panel(
            table,
            title=f"📋 Query Results ({len(results)} total)",
            border_style="green",
            padding=(1, 1)
        ))

    async def ask_question(self, question: str):
        """Process a user question."""
        if not self.current_session:
            self.console.print("[red]❌ No use case selected. Please select a use case first.[/red]")
            return

        if not self.api_client:
            self.console.print("[red]❌ API client not available[/red]")
            return

        # Display the question
        self.console.print(Panel(
            f"[bold cyan]Question:[/bold cyan] {question}",
            title="❓ User Query",
            border_style="blue",
            padding=(0, 1)
        ))

        # Execute query with progress indicator
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console,
            transient=True
        ) as progress:
            task = progress.add_task("🤖 Processing query...", total=None)

            start_time = time.time()
            response = await self.api_client.query(
                query_text=question,
                session_id=self.current_session.session_id,
                query_type="natural_language",
                repository=self.current_session.use_case.repository
            )
            processing_time = time.time() - start_time

            progress.update(task, description=f"✅ Query completed in {processing_time:.2f}s")

        if response:
            self.current_session.turn_count += 1
            self.format_query_response(response)
        else:
            self.console.print("[red]❌ Query failed or returned no response[/red]")

    def show_help(self):
        """Display help information."""
        help_text = """**Available Commands:**

**Navigation:**
• `select` or `s` - Select a different use case
• `status` or `info` - Show current system status
• `examples` or `ex` - Show example queries for current use case
• `help` or `?` - Show this help message
• `quit`, `exit`, or `q` - Exit the application

**Querying:**
• Simply type your question in natural language
• Examples:
  - "How many buildings are there?"
  - "What types of addresses exist?"
  - "Show me buildings with parking spaces"
  - "Compare residential and commercial buildings"
  - "What cities are represented in the data?"

**SPARQL Queries:**
• Prefix your query with `sparql:` to execute raw SPARQL
• Example: `sparql: SELECT ?s ?p ?o WHERE { ?s ?p ?o } LIMIT 10`

**Tips:**
• Use specific, clear questions for better results
• The system understands context from previous questions
• Check examples for inspiration on what to ask
"""

        self.console.print(Panel(
            Markdown(help_text),
            title="❓ Help & Commands",
            border_style="blue",
            padding=(1, 2)
        ))

    async def interactive_session(self):
        """Run the interactive query session."""
        if not self.current_session:
            self.console.print("[yellow]Please select a use case first[/yellow]")
            return

        self.console.print(f"\n[green]🎯 Active Use Case: {self.current_session.use_case.name}[/green]")
        self.console.print("[dim]Type 'help' for available commands, or ask a question directly[/dim]")

        while True:
            try:
                question = Prompt.ask(
                    f"\n[bold cyan]Query ({self.current_session.use_case.name})[/bold cyan]",
                    console=self.console
                )

                if not question.strip():
                    continue

                question_lower = question.lower().strip()

                # Handle commands
                if question_lower in ['quit', 'exit', 'q']:
                    self.console.print("[yellow]Goodbye! 👋[/yellow]")
                    break

                elif question_lower in ['select', 's']:
                    if await self.select_use_case():
                        self.console.print(f"[green]Switched to: {self.current_session.use_case.name}[/green]")
                    continue

                elif question_lower in ['status', 'info']:
                    self.print_status()
                    continue

                elif question_lower in ['examples', 'ex']:
                    await self.display_query_examples()
                    continue

                elif question_lower in ['help', '?']:
                    self.show_help()
                    continue

                # Handle SPARQL queries
                elif question.lower().startswith('sparql:'):
                    sparql_query = question[7:].strip()
                    if sparql_query:
                        response = await self.api_client.query(
                            query_text=sparql_query,
                            session_id=self.current_session.session_id,
                            query_type="sparql",
                            repository=self.current_session.use_case.repository
                        )
                        if response:
                            self.current_session.turn_count += 1
                            self.format_query_response(response)
                        else:
                            self.console.print("[red]❌ SPARQL query failed[/red]")
                    continue

                # Handle natural language questions
                await self.ask_question(question)

            except KeyboardInterrupt:
                self.console.print("\n[yellow]Use 'quit' to exit gracefully[/yellow]")
                continue
            except Exception as e:
                self.console.print(f"[red]❌ Unexpected error: {e}[/red]")
                continue

    async def run(self):
        """Main application entry point."""
        self.print_header()

        # Check API connection
        if not await self.check_api_connection():
            return

        # Load use cases
        async with APIClient() as client:
            self.api_client = client

            if not await self.load_use_cases():
                self.console.print("[red]Cannot proceed without use cases[/red]")
                return

            # Select initial use case
            if not await self.select_use_case():
                self.console.print("[yellow]No use case selected. Exiting.[/yellow]")
                return

            # Start interactive session
            await self.interactive_session()


# CLI Commands using Typer
@app.command()
def interactive():
    """Start interactive CLI session."""
    cli = RDFAgentCLI()
    asyncio.run(cli.run())


@app.command()
def query(
    text: str = typer.Argument(..., help="Query text to execute"),
    use_case: Optional[str] = typer.Option(None, "--use-case", "-u", help="Use case name"),
    sparql: bool = typer.Option(False, "--sparql", help="Execute as SPARQL query"),
    api_url: str = typer.Option(API_BASE_URL, "--api-url", help="API base URL")
):
    """Execute a single query and exit."""
    async def run_query():
        async with APIClient(api_url) as client:
            # Check connection
            if not await client.health_check():
                console.print(f"[red]❌ Cannot connect to API at {api_url}[/red]")
                return

            # Get repositories if use_case not specified
            if not use_case:
                repos = await client.get_repositories()
                if not repos:
                    console.print("[red]❌ No repositories found[/red]")
                    return
                selected_repo = repos[0]  # Use first available
                console.print(f"[yellow]Using repository: {selected_repo}[/yellow]")
            else:
                selected_repo = use_case

            # Create session for the repository
            session_id = await client.create_session({
                "use_case": selected_repo,
                "repository": selected_repo,
                "repository_type": "graphdb",
                "cli_mode": "single_query"
            })

            if not session_id:
                console.print("[red]❌ Failed to create session[/red]")
                return

            # Execute query against the repository
            console.print(f"[cyan]Executing query against repository '{selected_repo}': {text}[/cyan]")

            query_type = "sparql" if sparql else "natural_language"
            response = await client.query(text, session_id, query_type, selected_repo)

            if response:
                # Simple output format for CLI
                console.print(f"\n[green]Response:[/green] {response.get('response_text', 'No response')}")

                if response.get('sparql_query'):
                    console.print(f"\n[blue]SPARQL:[/blue]\n{response['sparql_query']}")

                results = response.get('results', [])
                if results:
                    console.print(f"\n[yellow]Results:[/yellow] {len(results)} items")
                    for i, result in enumerate(results[:3], 1):
                        console.print(f"  {i}. {result}")
                    if len(results) > 3:
                        console.print(f"  ... and {len(results) - 3} more")

                processing_time = response.get('processing_time', 0)
                console.print(f"\n[dim]Processing time: {processing_time:.2f}s[/dim]")
            else:
                console.print("[red]❌ Query failed[/red]")

    asyncio.run(run_query())


@app.command()
def list_usecases(
    api_url: str = typer.Option(API_BASE_URL, "--api-url", help="API base URL")
):
    """List available use cases."""
    async def list_repos():
        async with APIClient(api_url) as client:
            if not await client.health_check():
                console.print(f"[red]❌ Cannot connect to API at {api_url}[/red]")
                return

            repos = await client.get_repositories()
            if not repos:
                console.print("[yellow]No use cases found[/yellow]")
                return

            table = Table(show_header=True, header_style="bold magenta")
            table.add_column("Use Case", style="cyan")
            table.add_column("Repository", style="blue")
            table.add_column("Triples", style="green", justify="right")

            for repo in repos:
                size = await client.get_repository_size(repo)
                triple_count = f"{size:,}" if size else "Unknown"
                table.add_row(repo, repo, triple_count)

            console.print(table)

    asyncio.run(list_repos())


@app.command()
def status(
    api_url: str = typer.Option(API_BASE_URL, "--api-url", help="API base URL")
):
    """Check system status."""
    async def check_status():
        async with APIClient(api_url) as client:
            if not await client.health_check():
                console.print(f"[red]❌ API is not healthy at {api_url}[/red]")
                return

            status_data = await client.get_system_status()

            # Overall status
            overall_status = status_data.get("status", "unknown")
            status_color = "green" if overall_status == "healthy" else "yellow"
            console.print(f"[{status_color}]Overall Status: {overall_status.upper()}[/{status_color}]")

            # Services status
            services = status_data.get("services", {})
            if services:
                table = Table(show_header=True, header_style="bold magenta")
                table.add_column("Service", style="cyan")
                table.add_column("Status", style="white")

                for service, is_healthy in services.items():
                    status_text = "✅ Healthy" if is_healthy else "❌ Unhealthy"
                    table.add_row(service.title(), status_text)

                console.print(table)

    asyncio.run(check_status())


@app.command()
def examples(
    api_url: str = typer.Option(API_BASE_URL, "--api-url", help="API base URL")
):
    """Show example queries."""
    async def show_examples():
        async with APIClient(api_url) as client:
            if not await client.health_check():
                console.print(f"[red]❌ Cannot connect to API at {api_url}[/red]")
                return

            examples_data = await client.get_query_examples()

            # Natural language examples
            nl_examples = examples_data.get("natural_language_examples", [])
            if nl_examples:
                console.print("[bold cyan]Natural Language Examples:[/bold cyan]")
                for i, example in enumerate(nl_examples, 1):
                    console.print(f"  {i}. {example}")
                console.print()

            # SPARQL examples
            sparql_examples = examples_data.get("sparql_examples", [])
            if sparql_examples:
                console.print("[bold blue]SPARQL Examples:[/bold blue]")
                for i, example in enumerate(sparql_examples, 1):
                    console.print(f"  {i}. {example.get('description', 'SPARQL Query')}")
                    query = example.get('query', '').strip()
                    if query:
                        # Show first few lines
                        lines = query.split('\n')[:3]
                        for line in lines:
                            console.print(f"     {line}")
                        if len(query.split('\n')) > 3:
                            console.print("     ...")
                    console.print()

    asyncio.run(show_examples())


if __name__ == "__main__":
    # Default to interactive mode if no command specified
    if len(sys.argv) == 1:
        cli = RDFAgentCLI()
        asyncio.run(cli.run())
    else:
        app()
