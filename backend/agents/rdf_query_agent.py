"""
Enhanced RDF Query Agent for handling SPARQL queries and RDF data operations.
Converts natural language queries to SPARQL and executes them with dynamic TTL analysis.
"""
import logging
import time
from typing import Dict, Any, Optional, List, Union
from datetime import datetime
from pathlib import Path

from pydantic import BaseModel
from pydantic_ai import Agent, RunContext, ModelRetry
from pydantic_ai.models.openai import OpenAIChatModel
from pydantic_ai.providers.openrouter import OpenRouterProvider

from core.dependencies import Dependencies
from models.data_models import QueryResponse
from .ttl_analyzer import TTLAnalyzer, TTLAnalysis
from .rdf_models import (
    TTLContext, UserQuery, SPARQLQuery, QueryResult, AnalysisInsight,
    AgentStrategy, QAError, FinalAnswer, SPARQLOutput, StrategyOutput,
    TTLLoadRequest, TTLLoadResponse, AgentCapabilities, DataExploration,
    QuerySuggestion
)

logger = logging.getLogger(__name__)


class RDFQueryAgent:
    """Enhanced agent specialized in RDF queries and SPARQL operations with dynamic TTL analysis."""

    def __init__(self, dependencies: Dependencies):
        self.deps = dependencies
        self.ttl_analyzer = TTLAnalyzer()
        self.current_context: Optional[TTLContext] = None

        # Create PydanticAI agent with OpenRouter provider
        provider = OpenRouterProvider(api_key=dependencies.settings.ai.or_api_key)
        model = OpenAIChatModel('openai/gpt-4.1-mini', provider=provider)

        # Create the main QA agent
        self.qa_agent = Agent(
            model,
            deps_type=TTLContext,
            output_type=FinalAnswer,
            system_prompt=self._get_qa_system_prompt()
        )

        # Create the SPARQL generation agent
        self.sparql_agent = Agent(
            model,
            deps_type=str,  # TTL analysis text
            system_prompt=self._get_sparql_system_prompt()
        )

        # Register tools for the QA agent
        self._register_qa_tools()

        # Register output validator for SPARQL agent
        self._register_sparql_validator()

    def _get_qa_system_prompt(self) -> str:
        """Get the system prompt for the main QA agent."""
        return """You are an intelligent TTL (Turtle RDF) question-answering agent with advanced reasoning capabilities.

Your role is to help users understand and query TTL/RDF data using natural language questions. You have access to several tools:

1. get_ttl_analysis: Get complete analysis of the loaded TTL data structure
2. develop_strategy: Plan your approach for complex questions
3. generate_sparql_query: Create SPARQL queries to extract data
4. execute_sparql_query: Run queries against the TTL data
5. analyze_query_results: Extract insights from query results

CRITICAL: The TTL file structure and analysis is provided in your context dependencies. ALWAYS refer to the complete analysis to understand:
- Available classes and their counts
- Available properties and their usage
- Sample data values for accurate filtering
- Exact prefix definitions for namespace usage
- Relationships between entities

METHODOLOGY:
For each user question, you should:
1. First examine the TTL analysis to understand what data is available
2. Develop a strategy using develop_strategy tool based on available data
3. Generate appropriate SPARQL queries using generate_sparql_query with full context
4. Execute queries using execute_sparql_query
5. Analyze results using analyze_query_results
6. If needed, generate follow-up queries based on initial results and available data
7. Finally, synthesize all findings into a comprehensive answer

IMPORTANT RULES:
- ALWAYS consult the TTL analysis before generating queries
- Use exact prefix definitions and property names from the analysis
- Refer to sample data values for accurate string matching
- Consider class instance counts to provide context
- If a question asks about data that doesn't exist, explain what IS available
- Be thorough in your analysis and provide clear explanations
- Show your reasoning process throughout

You must return a FinalAnswer with your complete response."""

    def _get_sparql_system_prompt(self) -> str:
        """Get the system prompt for the SPARQL generation agent."""
        return """You are an expert SPARQL query generator.

Your task is to:
1. CAREFULLY analyze the comprehensive TTL file structure provided
2. Understand the user's natural language question
3. Generate a syntactically correct SPARQL query that answers the question
4. Use the EXACT prefixes, classes, and properties from the TTL analysis

CRITICAL Namespace Usage Rules:
- ALWAYS use the exact PREFIX definitions provided in the TTL analysis
- Properties are usually in a specific property namespace (e.g., prop:property-name)
- Classes are usually in a specific class namespace (e.g., ibpdi:ClassName)
- Study the "PROPERTIES BY NAMESPACE" section carefully to use correct prefixes
- Study the "CLASSES BY NAMESPACE" section to identify the right class prefixes
- Use the "SAMPLE DATA VALUES" to understand exact string formats and casing

Query Construction Guidelines:
- Always use proper PREFIX declarations from the TTL analysis
- Make queries as specific as possible to the user's question
- For counting questions, use COUNT(*) or COUNT(?variable)
- For listing questions, use SELECT with appropriate variables
- For existence questions, consider using ASK queries
- NEVER add LIMIT clauses unless explicitly requested by the user
- Always return complete results without artificial limitations
- When filtering by string values, use EXACT case matching from sample data
- If string matching fails, suggest case-insensitive approaches using FILTER with LCASE() or regex
- Return a QAError if the question cannot be answered with the available data

IMPORTANT: Pay special attention to which namespace each property belongs to!
Don't assume - check the TTL analysis carefully."""

    def _register_qa_tools(self):
        """Register tools for the main QA agent."""

        @self.qa_agent.tool
        async def get_ttl_analysis(ctx: RunContext[TTLContext]) -> str:
            """Get the complete TTL file analysis to understand available data structure, classes, properties, and sample values."""
            return ctx.deps.analysis_text

        @self.qa_agent.tool
        async def develop_strategy(ctx: RunContext[TTLContext], user_question: str) -> AgentStrategy:
            """Develop a strategic approach for answering a complex question."""
            analysis = ctx.deps.analysis

            # Parse available classes and properties
            available_classes = list(analysis.class_counts.keys())
            available_properties = []
            for props in analysis.property_by_namespace.values():
                available_properties.extend(props)

            question_lower = user_question.lower()

            # Enhanced strategy based on available data
            if any(word in question_lower for word in ["how many", "count", "number"]):
                # Find relevant entities for counting
                relevant_classes = [cls for cls in available_classes
                                  if any(word in cls.lower() for word in question_lower.split())]

                return AgentStrategy(
                    approach="Count-based analysis",
                    steps=[
                        f"Identify target entities (found: {', '.join(relevant_classes[:3])})" if relevant_classes else "Explore available entity types",
                        "Generate COUNT query for identified entities",
                        "Execute query and analyze results",
                        "Provide interpretation with context from available data"
                    ],
                    expected_queries=1,
                    reasoning=f"Question asks for quantitative info. Available data shows: {len(available_classes)} entity types with {sum(analysis.class_counts.values())} total instances",
                    relevant_entities=relevant_classes[:5]
                )

            elif any(word in question_lower for word in ["what", "which", "list", "show"]):
                # Find relevant entities and properties
                relevant_classes = [cls for cls in available_classes
                                  if any(word in cls.lower() for word in question_lower.split())]
                relevant_properties = [prop for prop in available_properties
                                     if any(word in prop.lower() for word in question_lower.split())]

                return AgentStrategy(
                    approach="Exploratory data retrieval",
                    steps=[
                        f"Identify relevant entities (found: {', '.join(relevant_classes[:3])})" if relevant_classes else "Explore available entity types",
                        f"Check for relevant properties (found: {', '.join(relevant_properties[:3])})" if relevant_properties else "Discover available properties",
                        "Generate initial exploratory query based on available data",
                        "Execute and analyze results",
                        "Generate follow-up queries if needed for complete information"
                    ],
                    expected_queries=2,
                    reasoning=f"Descriptive question. Found {len(relevant_classes)} relevant entity types and {len(relevant_properties)} relevant properties",
                    relevant_entities=relevant_classes[:5]
                )

            else:
                return AgentStrategy(
                    approach="General inquiry with data-aware exploration",
                    steps=[
                        f"Analyze available data structure ({len(available_classes)} entity types, {len(available_properties)} properties)",
                        "Generate broad exploratory query based on question context",
                        "Analyze initial results to identify relevant data patterns",
                        "Generate targeted follow-up queries for specific aspects",
                        "Synthesize comprehensive answer with full context"
                    ],
                    expected_queries=3,
                    reasoning=f"General question requiring flexible approach. Dataset contains: {', '.join(available_classes[:3])} with {sum(analysis.class_counts.values())} total entities",
                    relevant_entities=available_classes[:5]
                )

        @self.qa_agent.tool
        async def generate_sparql_query(ctx: RunContext[TTLContext], question: str, strategy_context: Optional[str] = None) -> SPARQLQuery:
            """Generate a SPARQL query to answer a specific question about the TTL data."""
            try:
                # Get the complete TTL analysis to provide to the SPARQL agent
                analysis = ctx.deps.analysis_text

                # Prepare the prompt for the SPARQL agent
                context_prompt = f"""Generate a SPARQL query to answer this question: "{question}"

Context from strategy: {strategy_context if strategy_context else "No additional strategy context"}

TTL File Analysis:
{analysis}

Please generate a SPARQL query that:
1. Uses the exact prefixes and namespaces from the analysis
2. Targets the appropriate classes and properties for this question
3. Returns the most relevant data to answer the question
4. Is syntactically correct and executable

Focus on the question's intent and use the available data structure intelligently."""

                # Use the SPARQL agent to generate the query
                result = await self.sparql_agent.run(context_prompt, deps=analysis)

                # Parse the result to extract SPARQL query
                query_text = result.output.strip()

                # Validate the generated query
                is_valid, query_type, error_msg = self.ttl_analyzer.validate_sparql_query(query_text)

                if not is_valid:
                    return SPARQLQuery(
                        query=f"# Error: Invalid SPARQL syntax: {error_msg}",
                        query_type="ERROR",
                        description=f"Generated query failed validation: {error_msg}",
                        confidence=0.1,
                        validation_passed=False
                    )

                # Return the successfully generated and validated SPARQL query
                return SPARQLQuery(
                    query=query_text,
                    query_type=query_type,
                    description=f"Generated SPARQL query to answer: {question}",
                    confidence=0.8,
                    validation_passed=True
                )

            except Exception as e:
                logger.error(f"Failed to generate SPARQL query: {e}")
                raise ModelRetry(f"Failed to generate SPARQL query using AI agent: {e}")

        @self.qa_agent.tool
        async def execute_sparql_query(ctx: RunContext[TTLContext], sparql_query: str) -> QueryResult:
            """Execute a SPARQL query against the loaded TTL graph."""
            try:
                start_time = time.time()

                # Execute the query
                results = list(ctx.deps.graph.query(sparql_query))

                execution_time = time.time() - start_time

                # Convert results to dictionaries - simplified approach for RDFLib
                data = []
                columns = []

                if results:
                    # Handle different types of SPARQL results
                    first_result = results[0]

                    if isinstance(first_result, bool):
                        # ASK query result
                        columns = ["result"]
                        data = [{"result": str(first_result)}]

                    else:
                        # For SELECT queries, try to extract variable names from the query
                        query_upper = sparql_query.upper()
                        if "SELECT" in query_upper:
                            # Extract variable names from SELECT clause
                            try:
                                select_part = query_upper.split("SELECT")[1].split("WHERE")[0]
                                # Simple extraction of variables (starting with ?)
                                import re
                                variables = re.findall(r'\?(\w+)', select_part)
                                if variables:
                                    columns = variables
                                else:
                                    columns = [f"col_{i}" for i in range(len(first_result) if hasattr(first_result, '__len__') else 1)]
                            except:
                                columns = [f"col_{i}" for i in range(len(first_result) if hasattr(first_result, '__len__') else 1)]
                        else:
                            columns = ["value"]

                        # Convert results to dictionaries
                        for result in results:
                            if len(columns) == 1:
                                # Single column result
                                data.append({columns[0]: str(result) if result is not None else None})
                            else:
                                # Multiple columns - assume result is iterable
                                row_dict = {}
                                try:
                                    for i, value in enumerate(result):
                                        if i < len(columns):
                                            row_dict[columns[i]] = str(value) if value is not None else None
                                    data.append(row_dict)
                                except (TypeError, IndexError):
                                    # Fallback: single value
                                    data.append({columns[0]: str(result) if result is not None else None})

                return QueryResult(
                    success=True,
                    data=data,
                    row_count=len(results),
                    columns=columns,
                    execution_time=execution_time,
                    sparql_query=sparql_query
                )

            except Exception as e:
                logger.error(f"Error executing SPARQL query: {e}")
                return QueryResult(
                    success=False,
                    error_message=str(e),
                    execution_time=0.0,
                    sparql_query=sparql_query
                )

        @self.qa_agent.tool
        async def analyze_query_results(ctx: RunContext[TTLContext], query_result: QueryResult, original_question: str) -> AnalysisInsight:
            """Analyze query results to extract insights relevant to the original question."""
            if not query_result.success:
                return AnalysisInsight(
                    insight=f"Query failed: {query_result.error_message}",
                    confidence=0.9,
                    supporting_data="Query execution error - this may indicate the data structure is different than expected"
                )

            if query_result.row_count == 0:
                # Get analysis to provide context about what data IS available
                analysis = ctx.deps.analysis
                available_entities = list(analysis.class_counts.keys())
                context = f"Available entity types: {', '.join(available_entities[:5])}" if available_entities else "No entity information available"

                return AnalysisInsight(
                    insight="No data found matching the query criteria",
                    confidence=0.8,
                    supporting_data=f"Empty result set. {context}",
                    data_coverage=f"Dataset contains {sum(analysis.class_counts.values())} total entities across {len(available_entities)} types"
                )

            # Enhanced analysis based on result structure and context
            analysis = ctx.deps.analysis
            total_entities = sum(analysis.class_counts.values())
            available_classes = list(analysis.class_counts.keys())

            insight = f"Found {query_result.row_count} results"
            if query_result.columns:
                insight += f" with columns: {', '.join(query_result.columns)}"

            # Add context about dataset coverage if it's a count query
            question_lower = original_question.lower()
            if any(word in question_lower for word in ["how many", "count", "number"]) and query_result.row_count == 1:
                # This is likely a count query result
                if query_result.data and query_result.data[0]:
                    count_value = list(query_result.data[0].values())[0]
                    if str(count_value).isdigit() and total_entities > 0:
                        percentage = (int(count_value) / total_entities) * 100
                        insight += f" (represents {percentage:.1f}% of total {total_entities} entities in dataset)"

            # Add coverage context for non-count queries
            elif total_entities > 0:
                coverage = (query_result.row_count / total_entities) * 100
                insight += f" (covering {coverage:.1f}% of dataset with {total_entities} total entities)"

            # Enhanced sample data with context
            sample_data = f"Sample results from {len(available_classes)} entity types available: "
            for i, row in enumerate(query_result.data[:3]):  # Show first 3 rows
                sample_data += f"Row {i+1}: {row}; "

            # Determine confidence based on result quality and context
            confidence = 0.7  # Default
            if query_result.row_count > 0:
                confidence = 0.9  # Good results
                if "count" in question_lower and query_result.row_count == 1:
                    confidence = 0.95  # Count queries are typically very accurate
                elif query_result.row_count < 5 and "list" in question_lower:
                    confidence = 0.75  # Might need more exploration for list queries

            return AnalysisInsight(
                insight=insight,
                confidence=confidence,
                supporting_data=sample_data.strip(),
                data_coverage=f"Dataset coverage: {len(available_classes)} entity types, {total_entities} total entities"
            )

    def _register_sparql_validator(self):
        """Register output validator for the SPARQL agent."""
        # No longer needed since we handle validation in generate_sparql_query
        pass

    async def load_ttl_file(self, file_path: str) -> TTLLoadResponse:
        """Load a TTL file and analyze its structure."""
        try:
            if not Path(file_path).exists():
                return TTLLoadResponse(
                    success=False,
                    message=f"File not found: {file_path}"
                )

            # Analyze the TTL file
            analysis = self.ttl_analyzer.analyze_ttl_file(file_path)

            # Create context
            self.current_context = TTLContext(analysis=analysis)

            return TTLLoadResponse(
                success=True,
                message=f"Successfully loaded TTL file: {Path(file_path).name}",
                analysis_summary=f"Loaded {analysis.total_triples} triples with {len(analysis.classes)} classes and {len(analysis.properties)} properties",
                total_triples=analysis.total_triples,
                classes_count=len(analysis.classes),
                properties_count=len(analysis.properties)
            )

        except Exception as e:
            logger.error(f"Error loading TTL file {file_path}: {e}")
            return TTLLoadResponse(
                success=False,
                message=f"Error loading TTL file: {str(e)}"
            )

    async def load_ttl_content(self, ttl_content: str, source_name: str = "content") -> TTLLoadResponse:
        """Load TTL content from string and analyze its structure."""
        try:
            # Analyze the TTL content
            analysis = self.ttl_analyzer.analyze_ttl_content(ttl_content, source_name)

            # Create context
            self.current_context = TTLContext(analysis=analysis)

            return TTLLoadResponse(
                success=True,
                message=f"Successfully loaded TTL content: {source_name}",
                analysis_summary=f"Loaded {analysis.total_triples} triples with {len(analysis.classes)} classes and {len(analysis.properties)} properties",
                total_triples=analysis.total_triples,
                classes_count=len(analysis.classes),
                properties_count=len(analysis.properties)
            )

        except Exception as e:
            logger.error(f"Error loading TTL content from {source_name}: {e}")
            return TTLLoadResponse(
                success=False,
                message=f"Error loading TTL content: {str(e)}"
            )

    async def _process_repository_query(self, query: str, repository: str, session_id: Optional[str] = None) -> Dict[str, Any]:
        """Process a query directly against a GraphDB repository without loading TTL data."""
        try:
            # Get repository statistics for context
            repo_size = await self.deps.graphdb_client.get_repository_size(repository)

            # Create a simple context for the repository
            context_info = f"""
Repository: {repository}
Total triples: {repo_size}
Available for SPARQL queries.

This is a GraphDB repository containing RDF data. You can generate SPARQL queries to answer questions about the data.
Common patterns:
- Count queries: SELECT (COUNT(*) as ?count) WHERE {{ ?s ?p ?o }}
- Type queries: SELECT DISTINCT ?type WHERE {{ ?s a ?type }}
- Property queries: SELECT DISTINCT ?p WHERE {{ ?s ?p ?o }}
- Sample data: SELECT ?s ?p ?o WHERE {{ ?s ?p ?o }} LIMIT 10
"""

            # For simple counting questions, provide direct answers
            if "how many" in query.lower() and ("triple" in query.lower() or "statement" in query.lower()):
                return {
                    "success": True,
                    "answer": f"The repository '{repository}' contains {repo_size:,} triples.",
                    "sparql_query": "SELECT (COUNT(*) as ?count) WHERE { ?s ?p ?o }",
                    "query_result": {"count": repo_size},
                    "repository": repository,
                    "processing_approach": "direct_count"
                }

            # For other queries, generate and execute SPARQL
            sparql_query = await self._generate_repository_sparql(query, repository, context_info)

            if sparql_query:
                # Execute the generated SPARQL query
                try:
                    result = await self.deps.graphdb_client.execute_sparql_query(sparql_query, repository)

                    # Process the results
                    if result and "results" in result and "bindings" in result["results"]:
                        bindings = result["results"]["bindings"]

                        return {
                            "success": True,
                            "answer": f"Query executed successfully. Found {len(bindings)} results.",
                            "sparql_query": sparql_query,
                            "query_result": {
                                "bindings": bindings[:10],  # Limit to first 10 results
                                "total_count": len(bindings)
                            },
                            "repository": repository,
                            "processing_approach": "sparql_execution"
                        }
                    else:
                        return {
                            "success": True,
                            "answer": "Query executed successfully but returned no results.",
                            "sparql_query": sparql_query,
                            "query_result": {"bindings": [], "total_count": 0},
                            "repository": repository,
                            "processing_approach": "sparql_execution"
                        }

                except Exception as e:
                    logger.error(f"Error executing SPARQL query: {e}")
                    return {
                        "success": False,
                        "error": f"Failed to execute SPARQL query: {str(e)}",
                        "sparql_query": sparql_query,
                        "repository": repository
                    }
            else:
                return {
                    "success": False,
                    "error": "Failed to generate SPARQL query for the given question",
                    "query": query,
                    "repository": repository
                }

        except Exception as e:
            logger.error(f"Error processing repository query: {e}")
            return {
                "success": False,
                "error": f"Error processing query against repository {repository}: {str(e)}",
                "query": query,
                "repository": repository
            }

    async def _generate_repository_sparql(self, question: str, repository: str, context_info: str) -> Optional[str]:
        """Generate a SPARQL query for a question about a repository."""
        try:
            # Simple pattern matching for common questions
            question_lower = question.lower()

            if "how many" in question_lower:
                if "type" in question_lower or "class" in question_lower:
                    return "SELECT (COUNT(DISTINCT ?type) as ?count) WHERE { ?s a ?type }"
                else:
                    return "SELECT (COUNT(*) as ?count) WHERE { ?s ?p ?o }"

            elif "what type" in question_lower or "what class" in question_lower:
                return "SELECT DISTINCT ?type WHERE { ?s a ?type } LIMIT 20"

            elif "show me" in question_lower or "list" in question_lower:
                if "type" in question_lower or "class" in question_lower:
                    return "SELECT DISTINCT ?type WHERE { ?s a ?type } LIMIT 20"
                else:
                    return "SELECT ?s ?p ?o WHERE { ?s ?p ?o } LIMIT 10"

            elif "what" in question_lower and ("propert" in question_lower or "predicate" in question_lower):
                return "SELECT DISTINCT ?p WHERE { ?s ?p ?o } LIMIT 20"

            else:
                # Default to showing sample data
                return "SELECT ?s ?p ?o WHERE { ?s ?p ?o } LIMIT 10"

        except Exception as e:
            logger.error(f"Error generating SPARQL query: {e}")
            return None

    async def _get_repository_ttl_data(self, repository: str) -> Optional[str]:
        """Get TTL data from a GraphDB repository by exporting all triples."""
        try:
            # Use SELECT query to get all triples from the repository
            select_query = "SELECT ?s ?p ?o WHERE { ?s ?p ?o } LIMIT 1000"

            # Execute the query against the specific repository
            result = await self.deps.graphdb_client.execute_sparql_query(
                select_query,
                repository=repository
            )

            if result and "results" in result and "bindings" in result["results"]:
                bindings = result["results"]["bindings"]

                if not bindings:
                    logger.warning(f"No triples found in repository {repository}")
                    return None

                # Convert SPARQL results back to TTL format
                ttl_lines = []

                # Add common prefixes
                ttl_lines.extend([
                    "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .",
                    "@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .",
                    "@prefix owl: <http://www.w3.org/2002/07/owl#> .",
                    "@prefix xsd: <http://www.w3.org/2001/XMLSchema#> .",
                    ""
                ])

                # Convert bindings to TTL triples
                for binding in bindings:
                    if "s" in binding and "p" in binding and "o" in binding:
                        subject = self._format_ttl_term(binding["s"])
                        predicate = self._format_ttl_term(binding["p"])
                        obj = self._format_ttl_term(binding["o"])
                        ttl_lines.append(f"{subject} {predicate} {obj} .")

                logger.info(f"Successfully converted {len(bindings)} triples from repository {repository} to TTL format")
                return "\n".join(ttl_lines)

            logger.warning(f"No valid results returned from repository {repository}")
            return None

        except Exception as e:
            logger.error(f"Error getting TTL data from repository {repository}: {e}")
            return None

    def _format_ttl_term(self, term_binding: Dict[str, str]) -> str:
        """Format a SPARQL binding term for TTL output."""
        term_type = term_binding.get("type", "literal")
        value = term_binding.get("value", "")

        if term_type == "uri":
            return f"<{value}>"
        elif term_type == "bnode":
            return value  # Blank nodes are already formatted
        else:  # literal
            datatype = term_binding.get("datatype")
            if datatype:
                return f'"{value}"^^<{datatype}>'
            else:
                return f'"{value}"'

    async def process_query(self, query: str, query_type: str = "natural_language",
                           session_id: Optional[str] = None, repository: Optional[str] = None) -> Dict[str, Any]:
        """Process a natural language query using the enhanced TTL-aware agent."""
        try:
            # If repository is specified, work directly with GraphDB
            if repository:
                logger.info(f"Processing query against GraphDB repository: {repository}")
                return await self._process_repository_query(query, repository, session_id)

            # Fallback to TTL-based processing
            if not self.current_context:
                return {
                    "error": "No TTL data loaded. Please load a TTL file first or specify a repository.",
                    "query": query,
                    "suggestions": ["Use load_ttl_file() or load_ttl_content() to load RDF data first, or specify a repository parameter"]
                }

            # Update session ID if provided
            if session_id:
                self.current_context.session_id = session_id

            # Add query to history
            self.current_context.query_history.append(query)

            # Use the QA agent to process the query
            result = await self.qa_agent.run(
                f"Please answer this question about the TTL data: {query}",
                deps=self.current_context
            )

            return {
                "response": result.output,
                "query_type": "natural_language",
                "original_query": query,
                "session_id": session_id,
                "data_summary": f"Analyzed {self.current_context.analysis.total_triples} triples"
            }

        except Exception as e:
            logger.error(f"Error processing RDF query: {e}")
            return {
                "error": str(e),
                "query": query,
                "query_type": "natural_language"
            }

    async def get_capabilities(self) -> AgentCapabilities:
        """Get current capabilities of the RDF agent."""
        if not self.current_context:
            return AgentCapabilities(
                ttl_loaded=False,
                sample_queries=["Please load a TTL file first using load_ttl_file() or load_ttl_content()"]
            )

        analysis = self.current_context.analysis

        # Generate sample queries based on available data
        sample_queries = []
        if analysis.class_counts:
            # Count queries
            for class_name in list(analysis.class_counts.keys())[:2]:
                sample_queries.append(f"How many {class_name.split(':')[-1].lower()} instances are there?")

            # List queries
            sample_queries.append(f"What are some examples of {list(analysis.class_counts.keys())[0].split(':')[-1].lower()}?")

        if analysis.sample_data:
            # Property queries
            for prop, values in list(analysis.sample_data.items())[:2]:
                sample_queries.append(f"Show me all items with {prop.split(':')[-1].replace('-', ' ')}")

        # Determine data domains
        data_domains = []
        for class_name in analysis.class_counts.keys():
            domain = class_name.split(':')[-1].lower()
            if domain not in data_domains:
                data_domains.append(domain)

        return AgentCapabilities(
            ttl_loaded=True,
            total_triples=analysis.total_triples,
            available_classes=list(analysis.class_counts.keys()),
            available_properties=list(analysis.sample_data.keys()),
            sample_queries=sample_queries,
            data_domains=data_domains[:5]
        )

    async def explore_data(self) -> DataExploration:
        """Explore the loaded RDF data and provide insights."""
        if not self.current_context:
            return DataExploration(
                summary="No TTL data loaded",
                key_entities=[],
                interesting_patterns=[],
                suggested_questions=[]
            )

        analysis = self.current_context.analysis

        # Generate summary
        summary = f"Dataset contains {analysis.total_triples} triples with {len(analysis.classes)} entity types and {len(analysis.properties)} properties."

        # Key entities (most common classes)
        key_entities = sorted(analysis.class_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        key_entity_names = [f"{name} ({count} instances)" for name, count in key_entities]

        # Interesting patterns
        patterns = []
        if analysis.class_counts:
            total_instances = sum(analysis.class_counts.values())
            for class_name, count in key_entities[:3]:
                percentage = (count / total_instances) * 100
                patterns.append(f"{class_name} represents {percentage:.1f}% of all entities")

        # Generate suggested questions
        suggested_questions = []
        for class_name, count in key_entities[:3]:
            suggested_questions.append(QuerySuggestion(
                question=f"How many {class_name.split(':')[-1].lower()} are there?",
                description=f"Count the total number of {class_name} instances",
                complexity="simple",
                expected_result_type="count"
            ))

        if analysis.sample_data:
            for prop, values in list(analysis.sample_data.items())[:2]:
                suggested_questions.append(QuerySuggestion(
                    question=f"What are the different values for {prop.split(':')[-1].replace('-', ' ')}?",
                    description=f"List unique values for the {prop} property",
                    complexity="medium",
                    expected_result_type="list"
                ))

        return DataExploration(
            summary=summary,
            key_entities=key_entity_names,
            interesting_patterns=patterns,
            suggested_questions=suggested_questions
        )

    def is_ttl_loaded(self) -> bool:
        """Check if TTL data is currently loaded."""
        return self.current_context is not None

    def get_analysis_summary(self) -> Optional[str]:
        """Get a summary of the current TTL analysis."""
        if not self.current_context:
            return None
        return self.current_context.analysis.analysis_text
