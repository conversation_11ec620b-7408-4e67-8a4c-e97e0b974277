"""
TTL File Analysis Module for RDF Query Agent.
Provides comprehensive analysis of TTL files to extract structure, prefixes, classes, and properties.
"""
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from pathlib import Path

import rdflib
from rdflib import Graph, Namespace
from rdflib.plugins.sparql import prepareQuery
from rdflib.exceptions import ParserError

logger = logging.getLogger(__name__)


@dataclass
class TTLAnalysis:
    """Comprehensive analysis of a TTL file."""
    file_path: str
    total_triples: int
    prefixes: Dict[str, str]
    classes: List[str]
    properties: List[str]
    class_counts: Dict[str, int]
    property_by_namespace: Dict[str, List[str]]
    class_by_namespace: Dict[str, List[str]]
    sample_data: Dict[str, List[str]]
    analysis_text: str
    graph: Graph


class TTLAnalyzer:
    """Analyzer for TTL files that extracts comprehensive structure information."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def analyze_ttl_file(self, ttl_file_path: str) -> TTLAnalysis:
        """
        Comprehensively analyze TTL file and provide detailed structure information.
        
        Args:
            ttl_file_path: Path to the TTL file to analyze
            
        Returns:
            TTLAnalysis object containing all extracted information
        """
        try:
            # Load the graph
            graph = Graph()
            graph.parse(ttl_file_path, format='turtle')
            
            # Get basic statistics
            total_triples = len(graph)
            
            # Get unique classes
            classes_query = """
            SELECT DISTINCT ?class WHERE {
                ?s a ?class .
            }
            """
            classes = [str(row[0]) for row in graph.query(classes_query)]
            
            # Get unique properties
            props_query = """
            SELECT DISTINCT ?prop WHERE {
                ?s ?prop ?o .
                FILTER(?prop != <http://www.w3.org/1999/02/22-rdf-syntax-ns#type>)
            }
            """
            properties = [str(row[0]) for row in graph.query(props_query)]
            
            # Get prefixes and their namespaces
            prefixes = dict(graph.namespaces())
            
            # Organize properties and classes by namespace
            property_by_namespace = self._organize_by_namespace(properties, prefixes)
            class_by_namespace = self._organize_by_namespace(classes, prefixes)
            
            # Get sample data for key properties
            sample_data = self._get_sample_data(graph, properties[:10], prefixes)
            
            # Get instance count per class
            class_counts = self._get_class_counts(graph, classes, prefixes)
            
            # Generate comprehensive analysis text
            analysis_text = self._generate_analysis_text(
                total_triples, classes, properties, prefixes,
                class_by_namespace, property_by_namespace,
                class_counts, sample_data
            )
            
            return TTLAnalysis(
                file_path=ttl_file_path,
                total_triples=total_triples,
                prefixes=prefixes,
                classes=classes,
                properties=properties,
                class_counts=class_counts,
                property_by_namespace=property_by_namespace,
                class_by_namespace=class_by_namespace,
                sample_data=sample_data,
                analysis_text=analysis_text,
                graph=graph
            )
            
        except Exception as e:
            self.logger.error(f"Error analyzing TTL file {ttl_file_path}: {e}")
            raise
    
    def analyze_ttl_content(self, ttl_content: str, source_name: str = "content") -> TTLAnalysis:
        """
        Analyze TTL content directly from string.
        
        Args:
            ttl_content: TTL content as string
            source_name: Name to use for the source (for logging/display)
            
        Returns:
            TTLAnalysis object containing all extracted information
        """
        try:
            # Load the graph from content
            graph = Graph()
            graph.parse(data=ttl_content, format='turtle')
            
            # Get basic statistics
            total_triples = len(graph)
            
            # Get unique classes
            classes_query = """
            SELECT DISTINCT ?class WHERE {
                ?s a ?class .
            }
            """
            classes = [str(row[0]) for row in graph.query(classes_query)]
            
            # Get unique properties
            props_query = """
            SELECT DISTINCT ?prop WHERE {
                ?s ?prop ?o .
                FILTER(?prop != <http://www.w3.org/1999/02/22-rdf-syntax-ns#type>)
            }
            """
            properties = [str(row[0]) for row in graph.query(props_query)]
            
            # Get prefixes and their namespaces
            prefixes = dict(graph.namespaces())
            
            # Organize properties and classes by namespace
            property_by_namespace = self._organize_by_namespace(properties, prefixes)
            class_by_namespace = self._organize_by_namespace(classes, prefixes)
            
            # Get sample data for key properties
            sample_data = self._get_sample_data(graph, properties[:10], prefixes)
            
            # Get instance count per class
            class_counts = self._get_class_counts(graph, classes, prefixes)
            
            # Generate comprehensive analysis text
            analysis_text = self._generate_analysis_text(
                total_triples, classes, properties, prefixes,
                class_by_namespace, property_by_namespace,
                class_counts, sample_data
            )
            
            return TTLAnalysis(
                file_path=source_name,
                total_triples=total_triples,
                prefixes=prefixes,
                classes=classes,
                properties=properties,
                class_counts=class_counts,
                property_by_namespace=property_by_namespace,
                class_by_namespace=class_by_namespace,
                sample_data=sample_data,
                analysis_text=analysis_text,
                graph=graph
            )
            
        except Exception as e:
            self.logger.error(f"Error analyzing TTL content from {source_name}: {e}")
            raise
    
    def _organize_by_namespace(self, items: List[str], prefixes: Dict[str, str]) -> Dict[str, List[str]]:
        """Organize items (classes or properties) by their namespace prefix."""
        items_by_namespace = {}
        
        for item in items:
            for prefix, namespace in prefixes.items():
                if item.startswith(str(namespace)):
                    if prefix not in items_by_namespace:
                        items_by_namespace[prefix] = []
                    local_name = item.replace(str(namespace), '')
                    items_by_namespace[prefix].append(f"{prefix}:{local_name}")
                    break
        
        return items_by_namespace
    
    def _get_sample_data(self, graph: Graph, properties: List[str], prefixes: Dict[str, str]) -> Dict[str, List[str]]:
        """Get sample data values for properties."""
        sample_data = {}
        
        for prop in properties:
            sample_query = f"""
            SELECT DISTINCT ?value WHERE {{
                ?s <{prop}> ?value .
            }} LIMIT 5
            """
            try:
                values = [str(row[0]) for row in graph.query(sample_query)]
                if values:
                    prop_local = prop
                    for prefix, namespace in prefixes.items():
                        if prop.startswith(str(namespace)):
                            prop_local = f"{prefix}:{prop.replace(str(namespace), '')}"
                            break
                    sample_data[prop_local] = values[:3]
            except Exception as e:
                self.logger.debug(f"Error getting sample data for property {prop}: {e}")
                continue
        
        return sample_data
    
    def _get_class_counts(self, graph: Graph, classes: List[str], prefixes: Dict[str, str]) -> Dict[str, int]:
        """Get instance count for each class."""
        class_counts = {}
        
        for cls in classes:
            count_query = f"""
            SELECT (COUNT(?s) as ?count) WHERE {{
                ?s a <{cls}> .
            }}
            """
            try:
                result = list(graph.query(count_query))
                if result:
                    cls_local = cls
                    for prefix, namespace in prefixes.items():
                        if cls.startswith(str(namespace)):
                            cls_local = f"{prefix}:{cls.replace(str(namespace), '')}"
                            break
                    class_counts[cls_local] = int(result[0][0])
            except Exception as e:
                self.logger.debug(f"Error getting count for class {cls}: {e}")
                continue
        
        return class_counts
    
    def _generate_analysis_text(
        self,
        total_triples: int,
        classes: List[str],
        properties: List[str],
        prefixes: Dict[str, str],
        class_by_namespace: Dict[str, List[str]],
        property_by_namespace: Dict[str, List[str]],
        class_counts: Dict[str, int],
        sample_data: Dict[str, List[str]]
    ) -> str:
        """Generate comprehensive analysis text."""
        
        analysis = f"""COMPREHENSIVE TTL FILE ANALYSIS:

=== BASIC STATISTICS ===
- Total triples: {total_triples}
- Classes found: {len(classes)}
- Properties found: {len(properties)}
- Prefixes defined: {len(prefixes)}

=== PREFIX DEFINITIONS ===
{chr(10).join([f"@prefix {prefix}: <{namespace}> ." for prefix, namespace in list(prefixes.items())])}

=== CLASSES BY NAMESPACE ===
{chr(10).join([f"{prefix} namespace classes: {', '.join(classes)}" for prefix, classes in class_by_namespace.items()])}

=== PROPERTIES BY NAMESPACE ===
{chr(10).join([f"{prefix} namespace properties: {', '.join(props[:10])}" + ("..." if len(props) > 10 else "") for prefix, props in property_by_namespace.items()])}

=== CLASS INSTANCE COUNTS ===
{chr(10).join([f"- {cls}: {count} instances" for cls, count in class_counts.items()])}

=== SAMPLE DATA VALUES ===
{chr(10).join([f"- {prop}: {', '.join(values)}" for prop, values in sample_data.items()])}

=== CRITICAL NAMESPACE MAPPING RULES ===
- For SPARQL queries, use the EXACT prefix definitions shown above
- Always check which namespace a property belongs to before writing queries
- Pay attention to the sample data values for exact string matching

=== QUERY CONSTRUCTION GUIDANCE ===
- Use PREFIX declarations exactly as shown in the prefix definitions
- Match property and class names with their correct namespaces
- Pay attention to the sample data values for exact string matching
- Remember that string values are case-sensitive
"""
        return analysis
    
    def validate_sparql_query(self, query: str) -> Tuple[bool, str, Optional[str]]:
        """
        Validate SPARQL syntax and return query type.
        
        Args:
            query: SPARQL query string to validate
            
        Returns:
            Tuple of (is_valid, query_type, error_message)
        """
        if not query or not isinstance(query, str):
            return False, "INVALID", "Query must be a non-empty string"
        
        try:
            prepareQuery(query)
            query_upper = query.upper().strip()
            if 'SELECT' in query_upper:
                return True, 'SELECT', None
            elif 'ASK' in query_upper:
                return True, 'ASK', None
            elif 'CONSTRUCT' in query_upper:
                return True, 'CONSTRUCT', None
            elif 'DESCRIBE' in query_upper:
                return True, 'DESCRIBE', None
            else:
                return True, 'UNKNOWN', None
        except Exception as e:
            return False, "INVALID", f"Invalid SPARQL syntax: {e}"
