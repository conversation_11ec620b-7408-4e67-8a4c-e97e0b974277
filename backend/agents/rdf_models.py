"""
Data models for the enhanced RDF Query Agent.
Provides structured data types for TTL analysis, SPARQL queries, and agent responses.
"""
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field
from dataclasses import dataclass
from rdflib import Graph


class UserQuery(BaseModel):
    """Represents a user's natural language question about RDF data."""
    question: str = Field(description="The user's natural language question")
    intent: Optional[str] = Field(description="Detected intent of the question", default=None)
    context: Optional[str] = Field(description="Additional context for the query", default=None)


class SPARQLQuery(BaseModel):
    """Represents a generated and validated SPARQL query."""
    query: str = Field(description="The SPARQL query string")
    query_type: str = Field(description="Type of SPARQL query (SELECT, ASK, etc.)")
    description: str = Field(description="What the query does")
    confidence: float = Field(description="Confidence in the query generation", default=0.8)
    validation_passed: bool = Field(description="Whether the query passed syntax validation", default=False)
    prefixes_used: List[str] = Field(description="List of prefixes used in the query", default=[])


class QueryResult(BaseModel):
    """Represents the result of executing a SPARQL query."""
    success: bool = Field(description="Whether the query executed successfully")
    data: List[Dict[str, Any]] = Field(description="Query result data", default=[])
    row_count: int = Field(description="Number of rows returned", default=0)
    columns: List[str] = Field(description="Column names in the result", default=[])
    execution_time: float = Field(description="Query execution time in seconds", default=0.0)
    error_message: Optional[str] = Field(description="Error message if query failed", default=None)
    sparql_query: Optional[str] = Field(description="The SPARQL query that was executed", default=None)


class AnalysisInsight(BaseModel):
    """Represents an analysis insight from query results."""
    insight: str = Field(description="The insight discovered from the data")
    confidence: float = Field(description="Confidence in the insight", default=0.8)
    supporting_data: str = Field(description="Summary of data supporting this insight")
    data_coverage: Optional[str] = Field(description="Information about data coverage", default=None)


class AgentStrategy(BaseModel):
    """Strategy for approaching the user's question."""
    approach: str = Field(description="High-level approach to take")
    steps: List[str] = Field(description="Specific steps to execute")
    expected_queries: int = Field(description="Expected number of queries needed")
    reasoning: str = Field(description="Why this strategy was chosen")
    relevant_entities: List[str] = Field(description="Relevant entities found in the data", default=[])


class QAError(BaseModel):
    """Represents an error in the QA pipeline."""
    error_message: str = Field(description="Error description")
    suggestions: str = Field(description="How to fix or rephrase the question")
    stage: str = Field(description="Which stage of the pipeline failed")
    available_data_summary: Optional[str] = Field(description="Summary of what data is available", default=None)


class FinalAnswer(BaseModel):
    """Final answer to the user's question."""
    answer: str = Field(description="Natural language answer to the user's question")
    confidence: float = Field(description="Overall confidence in the response", default=0.8)
    queries_used: List[str] = Field(description="List of SPARQL queries used", default=[])
    insights: List[str] = Field(description="Key insights discovered", default=[])
    methodology: str = Field(description="Explanation of the approach taken")
    data_summary: Optional[str] = Field(description="Summary of the data analyzed", default=None)


@dataclass
class TTLContext:
    """Context containing TTL analysis and graph for the RDF agent."""
    analysis: 'TTLAnalysis'  # Forward reference to avoid circular import
    session_id: Optional[str] = None
    query_history: List[str] = None
    
    def __post_init__(self):
        if self.query_history is None:
            self.query_history = []
    
    @property
    def graph(self) -> Graph:
        """Access to the RDF graph."""
        return self.analysis.graph
    
    @property
    def prefixes(self) -> Dict[str, str]:
        """Access to the prefixes."""
        return self.analysis.prefixes
    
    @property
    def analysis_text(self) -> str:
        """Access to the analysis text."""
        return self.analysis.analysis_text


class RDFQueryContext(BaseModel):
    """Enhanced context for the RDF query agent."""
    session_id: Optional[str] = None
    query_text: str
    query_type: str = "natural_language"
    ttl_loaded: bool = False
    available_classes: List[str] = Field(default=[])
    available_properties: List[str] = Field(default=[])
    prefixes: Dict[str, str] = Field(default={})


# Union types for agent outputs
SPARQLOutput = Union[SPARQLQuery, QAError]
StrategyOutput = Union[AgentStrategy, QAError]
AnalysisOutput = Union[AnalysisInsight, QAError]


class TTLLoadRequest(BaseModel):
    """Request to load TTL data."""
    source_type: str = Field(description="Type of source: 'file', 'content', 'url'")
    source: str = Field(description="File path, TTL content, or URL")
    name: Optional[str] = Field(description="Optional name for the TTL source", default=None)


class TTLLoadResponse(BaseModel):
    """Response from loading TTL data."""
    success: bool = Field(description="Whether the TTL was loaded successfully")
    message: str = Field(description="Success or error message")
    analysis_summary: Optional[str] = Field(description="Brief summary of the loaded data", default=None)
    total_triples: Optional[int] = Field(description="Number of triples loaded", default=None)
    classes_count: Optional[int] = Field(description="Number of unique classes", default=None)
    properties_count: Optional[int] = Field(description="Number of unique properties", default=None)


class QueryExecutionRequest(BaseModel):
    """Request to execute a SPARQL query."""
    query: str = Field(description="SPARQL query to execute")
    validate_first: bool = Field(description="Whether to validate syntax first", default=True)
    explain_results: bool = Field(description="Whether to provide explanation of results", default=True)


class QueryExecutionResponse(BaseModel):
    """Response from executing a SPARQL query."""
    success: bool = Field(description="Whether the query executed successfully")
    result: QueryResult = Field(description="Query execution result")
    explanation: Optional[str] = Field(description="Natural language explanation of results", default=None)
    suggestions: Optional[List[str]] = Field(description="Suggestions for follow-up queries", default=None)


class AgentCapabilities(BaseModel):
    """Describes the current capabilities of the RDF agent."""
    ttl_loaded: bool = Field(description="Whether TTL data is loaded")
    total_triples: int = Field(description="Number of triples available", default=0)
    available_classes: List[str] = Field(description="Available RDF classes", default=[])
    available_properties: List[str] = Field(description="Available RDF properties", default=[])
    sample_queries: List[str] = Field(description="Example queries that can be run", default=[])
    data_domains: List[str] = Field(description="Domains/topics covered by the data", default=[])


class QuerySuggestion(BaseModel):
    """Suggestion for a query the user might want to run."""
    question: str = Field(description="Natural language question")
    description: str = Field(description="What this query would reveal")
    complexity: str = Field(description="Query complexity: 'simple', 'medium', 'complex'")
    expected_result_type: str = Field(description="Type of result expected")


class DataExploration(BaseModel):
    """Results from exploring the loaded RDF data."""
    summary: str = Field(description="Overall summary of the data")
    key_entities: List[str] = Field(description="Most important entity types")
    interesting_patterns: List[str] = Field(description="Interesting patterns found in the data")
    suggested_questions: List[QuerySuggestion] = Field(description="Questions users might want to ask")
    data_quality_notes: List[str] = Field(description="Notes about data quality or completeness", default=[])
