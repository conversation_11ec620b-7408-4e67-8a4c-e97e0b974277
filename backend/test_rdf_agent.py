#!/usr/bin/env python3
"""
Test script for the enhanced RDF Query Agent.
Tests the agent with sample TTL files and realistic user queries.
"""
import asyncio
import os
import sys
from pathlib import Path
from typing import List, Dict, Any

# Add the backend directory to the Python path
sys.path.insert(0, str(Path(__file__).parent))

from agents.rdf_query_agent import RDFQueryAgent
from agents.ttl_analyzer import TTLAnalyzer
from core.dependencies import Dependencies
from config.settings import Settings


class MockDependencies:
    """Mock dependencies for testing."""
    def __init__(self):
        self.settings = MockSettings()
        self.graphdb_client = None


class MockSettings:
    """Mock settings for testing."""
    def __init__(self):
        self.ai = MockAISettings()


class MockAISettings:
    """Mock AI settings for testing."""
    def __init__(self):
        self.or_api_key = os.getenv("OR_API_KEY", "test-key")


async def test_ttl_analyzer():
    """Test the TTL analyzer with sample files."""
    print("🔍 Testing TTL Analyzer...")
    
    analyzer = TTLAnalyzer()
    
    # Test with example.ttl
    example_path = Path("test_data/example.ttl")
    if example_path.exists():
        print(f"📁 Analyzing {example_path}...")
        try:
            analysis = analyzer.analyze_ttl_file(str(example_path))
            print(f"✅ Successfully analyzed {analysis.total_triples} triples")
            print(f"   - Classes: {len(analysis.classes)}")
            print(f"   - Properties: {len(analysis.properties)}")
            print(f"   - Prefixes: {list(analysis.prefixes.keys())}")
            print(f"   - Sample classes: {list(analysis.class_counts.keys())[:3]}")
        except Exception as e:
            print(f"❌ Error analyzing {example_path}: {e}")
    else:
        print(f"⚠️  File not found: {example_path}")
    
    # Test with hobbit.ttl
    hobbit_path = Path("test_data/hobbit.ttl")
    if hobbit_path.exists():
        print(f"📁 Analyzing {hobbit_path}...")
        try:
            analysis = analyzer.analyze_ttl_file(str(hobbit_path))
            print(f"✅ Successfully analyzed {analysis.total_triples} triples")
            print(f"   - Classes: {len(analysis.classes)}")
            print(f"   - Properties: {len(analysis.properties)}")
            print(f"   - Prefixes: {list(analysis.prefixes.keys())}")
            print(f"   - Sample classes: {list(analysis.class_counts.keys())[:3]}")
        except Exception as e:
            print(f"❌ Error analyzing {hobbit_path}: {e}")
    else:
        print(f"⚠️  File not found: {hobbit_path}")
    
    print()


async def test_rdf_agent_basic():
    """Test basic RDF agent functionality."""
    print("🤖 Testing RDF Agent Basic Functionality...")
    
    deps = MockDependencies()
    agent = RDFQueryAgent(deps)
    
    # Test without loaded data
    print("📊 Testing capabilities without data...")
    capabilities = await agent.get_capabilities()
    print(f"✅ TTL loaded: {capabilities.ttl_loaded}")
    print(f"   - Sample queries: {capabilities.sample_queries[:2]}")
    
    # Test data exploration without data
    print("🔍 Testing data exploration without data...")
    exploration = await agent.explore_data()
    print(f"✅ Summary: {exploration.summary}")
    
    print()


async def test_rdf_agent_with_data():
    """Test RDF agent with loaded TTL data."""
    print("🤖 Testing RDF Agent with TTL Data...")
    
    deps = MockDependencies()
    agent = RDFQueryAgent(deps)
    
    # Test loading TTL file
    example_path = Path("test_data/example.ttl")
    if example_path.exists():
        print(f"📁 Loading {example_path}...")
        response = await agent.load_ttl_file(str(example_path))
        if response.success:
            print(f"✅ {response.message}")
            print(f"   - {response.analysis_summary}")
            
            # Test capabilities with data
            print("📊 Testing capabilities with data...")
            capabilities = await agent.get_capabilities()
            print(f"✅ TTL loaded: {capabilities.ttl_loaded}")
            print(f"   - Total triples: {capabilities.total_triples}")
            print(f"   - Classes: {len(capabilities.available_classes)}")
            print(f"   - Sample queries: {capabilities.sample_queries[:3]}")
            
            # Test data exploration with data
            print("🔍 Testing data exploration with data...")
            exploration = await agent.explore_data()
            print(f"✅ Summary: {exploration.summary}")
            print(f"   - Key entities: {exploration.key_entities[:3]}")
            print(f"   - Suggested questions: {len(exploration.suggested_questions)}")
            
            # Show analysis summary
            print("📋 Analysis Summary Preview:")
            summary = agent.get_analysis_summary()
            if summary:
                lines = summary.split('\n')[:15]  # First 15 lines
                for line in lines:
                    print(f"   {line}")
                print("   ...")
        else:
            print(f"❌ Failed to load: {response.message}")
    else:
        print(f"⚠️  File not found: {example_path}")
    
    print()


async def test_sample_queries():
    """Test the agent with sample natural language queries."""
    print("💬 Testing Sample Natural Language Queries...")
    
    deps = MockDependencies()
    agent = RDFQueryAgent(deps)
    
    # Load sample data
    example_path = Path("test_data/example.ttl")
    if not example_path.exists():
        print(f"⚠️  File not found: {example_path}")
        return
    
    response = await agent.load_ttl_file(str(example_path))
    if not response.success:
        print(f"❌ Failed to load data: {response.message}")
        return
    
    print(f"✅ Loaded data: {response.analysis_summary}")
    
    # Sample queries to test
    sample_queries = [
        "How many addresses are there?",
        "What cities are represented in the data?",
        "Show me some examples of addresses",
        "What countries are in the dataset?",
        "How many buildings are there?",
        "What are the different postal codes?",
    ]
    
    print(f"🔍 Testing {len(sample_queries)} sample queries...")
    
    for i, query in enumerate(sample_queries, 1):
        print(f"\n📝 Query {i}: {query}")
        try:
            # Note: This would normally call the AI agent, but we'll just test the setup
            result = await agent.process_query(query)
            
            if "error" in result:
                print(f"❌ Error: {result['error']}")
                if "suggestions" in result:
                    print(f"   Suggestions: {result['suggestions']}")
            else:
                print(f"✅ Query processed successfully")
                print(f"   Response type: {result.get('query_type', 'unknown')}")
                if "data_summary" in result:
                    print(f"   Data summary: {result['data_summary']}")
        except Exception as e:
            print(f"❌ Exception: {e}")
    
    print()


async def test_hobbit_data():
    """Test the agent with hobbit benchmark data."""
    print("🏔️ Testing with Hobbit Benchmark Data...")
    
    deps = MockDependencies()
    agent = RDFQueryAgent(deps)
    
    # Load hobbit data
    hobbit_path = Path("test_data/hobbit.ttl")
    if not hobbit_path.exists():
        print(f"⚠️  File not found: {hobbit_path}")
        return
    
    response = await agent.load_ttl_file(str(hobbit_path))
    if not response.success:
        print(f"❌ Failed to load data: {response.message}")
        return
    
    print(f"✅ Loaded data: {response.analysis_summary}")
    
    # Get capabilities
    capabilities = await agent.get_capabilities()
    print(f"📊 Capabilities:")
    print(f"   - Total triples: {capabilities.total_triples}")
    print(f"   - Available classes: {capabilities.available_classes[:5]}")
    print(f"   - Data domains: {capabilities.data_domains}")
    
    # Explore data
    exploration = await agent.explore_data()
    print(f"🔍 Data Exploration:")
    print(f"   - Summary: {exploration.summary}")
    print(f"   - Key entities: {exploration.key_entities[:3]}")
    print(f"   - Interesting patterns: {exploration.interesting_patterns[:2]}")
    
    # Sample queries for hobbit data
    hobbit_queries = [
        "How many benchmarks are there?",
        "What KPIs are measured?",
        "Show me the different benchmark types",
        "What systems are being evaluated?",
    ]
    
    print(f"💬 Testing {len(hobbit_queries)} hobbit-specific queries...")
    
    for i, query in enumerate(hobbit_queries, 1):
        print(f"\n📝 Query {i}: {query}")
        try:
            result = await agent.process_query(query)
            
            if "error" in result:
                print(f"❌ Error: {result['error']}")
            else:
                print(f"✅ Query processed successfully")
        except Exception as e:
            print(f"❌ Exception: {e}")
    
    print()


async def main():
    """Run all tests."""
    print("🚀 Starting Enhanced RDF Query Agent Tests\n")
    
    try:
        await test_ttl_analyzer()
        await test_rdf_agent_basic()
        await test_rdf_agent_with_data()
        await test_sample_queries()
        await test_hobbit_data()
        
        print("✅ All tests completed!")
        
    except Exception as e:
        print(f"❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
