"""
Tests for the enhanced RDF Query Agent with dynamic TTL analysis.
"""
import pytest
import asyncio
from pathlib import Path
from unittest.mock import Mock, AsyncMock

from agents.rdf_query_agent import RDFQueryAgent
from agents.ttl_analyzer import TTLAnalyzer
from agents.rdf_models import TTLLoadResponse, AgentCapabilities, DataExploration
from core.dependencies import Dependencies
from config.settings import Settings


@pytest.fixture
def mock_dependencies():
    """Create mock dependencies for testing."""
    deps = Mock(spec=Dependencies)
    deps.settings = Mock(spec=Settings)
    deps.settings.ai = Mock()
    deps.settings.ai.or_api_key = "test-api-key"
    deps.graphdb_client = None  # Not needed for TTL-based agent
    return deps


@pytest.fixture
def sample_ttl_content():
    """Sample TTL content for testing."""
    return """
@prefix ex: <http://example.org/> .
@prefix foaf: <http://xmlns.com/foaf/0.1/> .
@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .

ex:alice a foaf:Person ;
    foaf:name "<PERSON>" ;
    foaf:age 30 ;
    foaf:knows ex:bob .

ex:bob a foaf:Person ;
    foaf:name "<PERSON>" ;
    foaf:age 25 ;
    foaf:knows ex:alice .

ex:charlie a foaf:Person ;
    foaf:name "Charlie Brown" ;
    foaf:age 35 .
"""


@pytest.fixture
def rdf_agent(mock_dependencies):
    """Create RDF query agent for testing."""
    return RDFQueryAgent(mock_dependencies)


class TestTTLAnalyzer:
    """Test the TTL analyzer functionality."""
    
    def test_analyze_ttl_content(self, sample_ttl_content):
        """Test analyzing TTL content."""
        analyzer = TTLAnalyzer()
        analysis = analyzer.analyze_ttl_content(sample_ttl_content, "test_content")
        
        assert analysis.total_triples > 0
        # Check that Person class is found (either as full URI or local name)
        class_names = [cls.split('/')[-1] for cls in analysis.classes]
        assert "Person" in class_names or any("Person" in cls for cls in analysis.classes)
        assert len(analysis.prefixes) >= 3  # ex, foaf, rdf
        assert "ex" in analysis.prefixes
        assert "foaf" in analysis.prefixes
        assert analysis.class_counts["foaf:Person"] == 3
    
    def test_validate_sparql_query(self):
        """Test SPARQL query validation."""
        analyzer = TTLAnalyzer()
        
        # Valid SELECT query
        valid_query = """
        PREFIX foaf: <http://xmlns.com/foaf/0.1/>
        SELECT ?name WHERE {
            ?person a foaf:Person ;
                    foaf:name ?name .
        }
        """
        is_valid, query_type, error = analyzer.validate_sparql_query(valid_query)
        assert is_valid
        assert query_type == "SELECT"
        assert error is None
        
        # Invalid query
        invalid_query = "INVALID SPARQL QUERY"
        is_valid, query_type, error = analyzer.validate_sparql_query(invalid_query)
        assert not is_valid
        assert query_type == "INVALID"
        assert error is not None


class TestRDFQueryAgent:
    """Test the enhanced RDF query agent."""
    
    @pytest.mark.asyncio
    async def test_load_ttl_content(self, rdf_agent, sample_ttl_content):
        """Test loading TTL content."""
        response = await rdf_agent.load_ttl_content(sample_ttl_content, "test_sample")
        
        assert response.success
        assert response.total_triples > 0
        assert response.classes_count > 0
        assert response.properties_count > 0
        assert "test_sample" in response.message
    
    @pytest.mark.asyncio
    async def test_load_ttl_file_not_found(self, rdf_agent):
        """Test loading non-existent TTL file."""
        response = await rdf_agent.load_ttl_file("nonexistent.ttl")
        
        assert not response.success
        assert "not found" in response.message.lower()
    
    def test_is_ttl_loaded(self, rdf_agent, sample_ttl_content):
        """Test checking if TTL is loaded."""
        assert not rdf_agent.is_ttl_loaded()
        
        # Load TTL content (synchronous for this test)
        analyzer = TTLAnalyzer()
        analysis = analyzer.analyze_ttl_content(sample_ttl_content, "test")
        from agents.rdf_models import TTLContext
        rdf_agent.current_context = TTLContext(analysis=analysis)
        
        assert rdf_agent.is_ttl_loaded()
    
    @pytest.mark.asyncio
    async def test_get_capabilities_no_data(self, rdf_agent):
        """Test getting capabilities when no data is loaded."""
        capabilities = await rdf_agent.get_capabilities()
        
        assert not capabilities.ttl_loaded
        assert capabilities.total_triples == 0
        assert len(capabilities.sample_queries) > 0
        assert "load" in capabilities.sample_queries[0].lower()
    
    @pytest.mark.asyncio
    async def test_get_capabilities_with_data(self, rdf_agent, sample_ttl_content):
        """Test getting capabilities when data is loaded."""
        # Load data first
        await rdf_agent.load_ttl_content(sample_ttl_content, "test")
        
        capabilities = await rdf_agent.get_capabilities()
        
        assert capabilities.ttl_loaded
        assert capabilities.total_triples > 0
        assert len(capabilities.available_classes) > 0
        assert len(capabilities.sample_queries) > 0
        assert "foaf:Person" in capabilities.available_classes
    
    @pytest.mark.asyncio
    async def test_explore_data_no_data(self, rdf_agent):
        """Test data exploration when no data is loaded."""
        exploration = await rdf_agent.explore_data()
        
        assert "No TTL data loaded" in exploration.summary
        assert len(exploration.key_entities) == 0
        assert len(exploration.suggested_questions) == 0
    
    @pytest.mark.asyncio
    async def test_explore_data_with_data(self, rdf_agent, sample_ttl_content):
        """Test data exploration when data is loaded."""
        # Load data first
        await rdf_agent.load_ttl_content(sample_ttl_content, "test")
        
        exploration = await rdf_agent.explore_data()
        
        assert "triples" in exploration.summary
        assert len(exploration.key_entities) > 0
        assert len(exploration.suggested_questions) > 0
        assert any("Person" in entity for entity in exploration.key_entities)
    
    @pytest.mark.asyncio
    async def test_process_query_no_data(self, rdf_agent):
        """Test processing query when no data is loaded."""
        result = await rdf_agent.process_query("How many people are there?")
        
        assert "error" in result
        assert "No TTL data loaded" in result["error"]
        assert "suggestions" in result
    
    def test_get_analysis_summary_no_data(self, rdf_agent):
        """Test getting analysis summary when no data is loaded."""
        summary = rdf_agent.get_analysis_summary()
        assert summary is None
    
    def test_get_analysis_summary_with_data(self, rdf_agent, sample_ttl_content):
        """Test getting analysis summary when data is loaded."""
        # Load data first (synchronous for this test)
        analyzer = TTLAnalyzer()
        analysis = analyzer.analyze_ttl_content(sample_ttl_content, "test")
        from agents.rdf_models import TTLContext
        rdf_agent.current_context = TTLContext(analysis=analysis)
        
        summary = rdf_agent.get_analysis_summary()
        assert summary is not None
        assert "COMPREHENSIVE TTL FILE ANALYSIS" in summary
        assert "PREFIX DEFINITIONS" in summary
        assert "CLASSES BY NAMESPACE" in summary


class TestIntegrationWithSampleFiles:
    """Integration tests with actual sample TTL files."""
    
    @pytest.fixture
    def example_ttl_path(self):
        """Path to the example TTL file."""
        return Path("backend/test_data/example.ttl")
    
    @pytest.fixture
    def hobbit_ttl_path(self):
        """Path to the hobbit TTL file."""
        return Path("backend/test_data/hobbit.ttl")
    
    @pytest.mark.asyncio
    async def test_load_example_ttl(self, rdf_agent, example_ttl_path):
        """Test loading the example TTL file."""
        if not example_ttl_path.exists():
            pytest.skip("Example TTL file not found")
        
        response = await rdf_agent.load_ttl_file(str(example_ttl_path))
        
        assert response.success
        assert response.total_triples > 0
        assert "Address" in str(response.analysis_summary) or "Building" in str(response.analysis_summary)
    
    @pytest.mark.asyncio
    async def test_load_hobbit_ttl(self, rdf_agent, hobbit_ttl_path):
        """Test loading the hobbit TTL file."""
        if not hobbit_ttl_path.exists():
            pytest.skip("Hobbit TTL file not found")
        
        response = await rdf_agent.load_ttl_file(str(hobbit_ttl_path))
        
        assert response.success
        assert response.total_triples > 0
        assert "Benchmark" in str(response.analysis_summary) or "hobbit" in str(response.analysis_summary)
    
    @pytest.mark.asyncio
    async def test_capabilities_with_example_file(self, rdf_agent, example_ttl_path):
        """Test capabilities with the example TTL file."""
        if not example_ttl_path.exists():
            pytest.skip("Example TTL file not found")
        
        # Load the file
        await rdf_agent.load_ttl_file(str(example_ttl_path))
        
        # Get capabilities
        capabilities = await rdf_agent.get_capabilities()
        
        assert capabilities.ttl_loaded
        assert capabilities.total_triples > 0
        assert len(capabilities.available_classes) > 0
        assert len(capabilities.sample_queries) > 0
        
        # Should have building/address related classes
        class_names = " ".join(capabilities.available_classes).lower()
        assert "address" in class_names or "building" in class_names
    
    @pytest.mark.asyncio
    async def test_exploration_with_hobbit_file(self, rdf_agent, hobbit_ttl_path):
        """Test data exploration with the hobbit TTL file."""
        if not hobbit_ttl_path.exists():
            pytest.skip("Hobbit TTL file not found")
        
        # Load the file
        await rdf_agent.load_ttl_file(str(hobbit_ttl_path))
        
        # Explore data
        exploration = await rdf_agent.explore_data()
        
        assert "triples" in exploration.summary
        assert len(exploration.key_entities) > 0
        assert len(exploration.suggested_questions) > 0
        
        # Should have benchmark-related entities
        entities_text = " ".join(exploration.key_entities).lower()
        assert "benchmark" in entities_text or "kpi" in entities_text or "hobbit" in entities_text


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
