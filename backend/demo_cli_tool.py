#!/usr/bin/env python3
"""
Demo script for the RDF Agent CLI Tool.
Shows the CLI functionality with the enhanced RDF query agent.
"""

import asyncio
import os
import sys
from pathlib import Path
from typing import Dict, List, Optional, Any

# Add the backend directory to the Python path
sys.path.insert(0, str(Path(__file__).parent))

# Rich imports for beautiful CLI
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.prompt import Prompt, Confirm, IntPrompt
from rich.text import Text
from rich.table import Table
from rich.markdown import Markdown
from rich import box
from rich.align import Align
from rich.syntax import Syntax

# Import our enhanced RDF query agent
from agents.rdf_query_agent import RDFQueryAgent
from core.dependencies import get_dependencies

console = Console()


class MockUseCaseManager:
    """Mock use case manager for demonstration."""
    
    def __init__(self):
        self.use_cases = [
            {
                "name": "Example Building Data",
                "description": "Sample building and address data from example.ttl",
                "file_path": "test_data/example.ttl",
                "triple_count": 2172
            },
            {
                "name": "Hobbit Benchmark Data", 
                "description": "Benchmark evaluation data from hobbit.ttl",
                "file_path": "test_data/hobbit.ttl",
                "triple_count": 109
            }
        ]
    
    def get_available_use_cases(self) -> List[Dict[str, Any]]:
        """Get list of available use cases."""
        available = []
        for use_case in self.use_cases:
            file_path = Path(use_case["file_path"])
            if file_path.exists():
                available.append(use_case)
        return available


class CLIDemo:
    """CLI demonstration class."""
    
    def __init__(self):
        self.console = Console()
        self.use_case_manager = MockUseCaseManager()
        self.rdf_agent: Optional[RDFQueryAgent] = None
        self.current_use_case: Optional[Dict[str, Any]] = None
    
    def print_header(self):
        """Print application header."""
        header_text = """
🚀 RDF Agent CLI Tool - DEMO
Interactive Use Case Querying with Enhanced RDF Agent
        """
        
        header_panel = Panel(
            Align.center(header_text),
            border_style="bright_blue",
            box=box.DOUBLE_EDGE,
            padding=(1, 2)
        )
        
        self.console.print(header_panel)
        self.console.print()
    
    def display_use_cases(self, use_cases: List[Dict[str, Any]]):
        """Display available use cases."""
        table = Table(show_header=True, header_style="bold magenta", box=box.ROUNDED)
        table.add_column("#", style="dim", width=3)
        table.add_column("Use Case", style="cyan", min_width=25)
        table.add_column("Description", style="blue", min_width=30)
        table.add_column("Triples", style="green", justify="right")
        table.add_column("Status", style="yellow")
        
        for i, use_case in enumerate(use_cases, 1):
            file_path = Path(use_case["file_path"])
            status = "✅ Available" if file_path.exists() else "❌ Missing"
            status_style = "green" if file_path.exists() else "red"
            
            table.add_row(
                str(i),
                use_case["name"],
                use_case["description"],
                f"{use_case['triple_count']:,}",
                f"[{status_style}]{status}[/{status_style}]"
            )
        
        panel = Panel(
            table,
            title="🎯 Available Use Cases",
            border_style="blue",
            padding=(1, 2)
        )
        
        self.console.print(panel)
    
    async def select_use_case(self) -> bool:
        """Allow user to select a use case."""
        use_cases = self.use_case_manager.get_available_use_cases()
        
        if not use_cases:
            self.console.print("[red]❌ No use cases available. Please ensure TTL files exist in test_data/[/red]")
            return False
        
        self.display_use_cases(use_cases)
        
        try:
            choice = IntPrompt.ask(
                "\n[cyan]Select a use case (number)[/cyan]",
                default=1,
                show_default=True,
                console=self.console
            )
            
            if 1 <= choice <= len(use_cases):
                selected_use_case = use_cases[choice - 1]
                
                # Initialize RDF agent
                deps = await get_dependencies()
                self.rdf_agent = RDFQueryAgent(deps)
                
                # Load TTL file
                with Progress(
                    SpinnerColumn(),
                    TextColumn("[progress.description]{task.description}"),
                    console=self.console,
                    transient=True
                ) as progress:
                    task = progress.add_task("Loading TTL data...", total=None)
                    
                    response = await self.rdf_agent.load_ttl_file(selected_use_case["file_path"])
                    
                    if response.success:
                        progress.update(task, description="✅ TTL data loaded successfully!")
                        self.current_use_case = selected_use_case
                        
                        self.console.print(f"[green]✅ Selected use case: {selected_use_case['name']}[/green]")
                        self.console.print(f"[dim]{response.message}[/dim]")
                        return True
                    else:
                        progress.update(task, description="❌ Failed to load TTL data")
                        self.console.print(f"[red]❌ Failed to load use case: {response.message}[/red]")
                        return False
            else:
                self.console.print("[red]Invalid selection[/red]")
                return False
                
        except (ValueError, KeyboardInterrupt):
            self.console.print("[yellow]Selection cancelled[/yellow]")
            return False
    
    def print_status(self):
        """Print current system status."""
        status_table = Table(show_header=False, box=box.MINIMAL)
        status_table.add_column("Item", style="cyan")
        status_table.add_column("Value", style="green")
        
        if self.current_use_case and self.rdf_agent:
            status_table.add_row("🎯 Active Use Case", self.current_use_case["name"])
            status_table.add_row("📁 Data File", self.current_use_case["file_path"])
            status_table.add_row("🔢 Triple Count", f"{self.current_use_case['triple_count']:,}")
            status_table.add_row("🤖 Agent Status", "Ready")
        else:
            status_table.add_row("🎯 Active Use Case", "[yellow]None selected[/yellow]")
        
        available_count = len(self.use_case_manager.get_available_use_cases())
        status_table.add_row("📊 Available Use Cases", str(available_count))
        
        status_panel = Panel(
            status_table,
            title="📊 System Status",
            border_style="green" if self.current_use_case else "yellow",
            padding=(0, 1)
        )
        
        self.console.print(status_panel)
    
    async def show_capabilities(self):
        """Show agent capabilities for current use case."""
        if not self.rdf_agent:
            self.console.print("[red]❌ No use case loaded[/red]")
            return
        
        capabilities = await self.rdf_agent.get_capabilities()
        
        cap_info = f"""**TTL Loaded:** {'✅ Yes' if capabilities.ttl_loaded else '❌ No'}
**Total Triples:** {capabilities.total_triples:,}
**Available Classes:** {len(capabilities.available_classes)}
**Data Domains:** {', '.join(capabilities.data_domains)}

**Sample Queries:**"""
        
        for i, query in enumerate(capabilities.sample_queries[:3], 1):
            cap_info += f"\n{i}. {query}"
        
        self.console.print(Panel(
            Markdown(cap_info),
            title="🔍 Agent Capabilities",
            border_style="cyan",
            padding=(1, 2)
        ))
    
    async def explore_data(self):
        """Explore the loaded data."""
        if not self.rdf_agent:
            self.console.print("[red]❌ No use case loaded[/red]")
            return
        
        exploration = await self.rdf_agent.explore_data()
        
        explore_info = f"""**Summary:** {exploration.summary}

**Key Entities:**"""
        
        for entity in exploration.key_entities[:5]:
            explore_info += f"\n• {entity}"
        
        if exploration.interesting_patterns:
            explore_info += f"\n\n**Interesting Patterns:**"
            for pattern in exploration.interesting_patterns[:3]:
                explore_info += f"\n• {pattern}"
        
        if exploration.suggested_questions:
            explore_info += f"\n\n**Suggested Questions:**"
            for i, suggestion in enumerate(exploration.suggested_questions[:3], 1):
                explore_info += f"\n{i}. {suggestion.question}"
        
        self.console.print(Panel(
            Markdown(explore_info),
            title="🔍 Data Exploration",
            border_style="blue",
            padding=(1, 2)
        ))
    
    async def ask_question(self, question: str):
        """Process a user question."""
        if not self.rdf_agent:
            self.console.print("[red]❌ No use case loaded[/red]")
            return
        
        # Display the question
        self.console.print(Panel(
            f"[bold cyan]Question:[/bold cyan] {question}",
            title="❓ User Query",
            border_style="blue",
            padding=(0, 1)
        ))
        
        # Process query with progress indicator
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console,
            transient=True
        ) as progress:
            task = progress.add_task("🤖 Processing query...", total=None)
            
            try:
                result = await self.rdf_agent.process_query(question)
                progress.update(task, description="✅ Query completed!")

                # Handle different result types
                if hasattr(result, 'answer'):
                    # FinalAnswer object
                    self.console.print(Panel(
                        result.answer,
                        title="💬 Answer",
                        border_style="green",
                        padding=(1, 2)
                    ))

                    if hasattr(result, 'confidence') and result.confidence:
                        confidence_text = f"Confidence: {result.confidence:.1%}"
                        self.console.print(f"[dim]{confidence_text}[/dim]")

                    if hasattr(result, 'queries_used') and result.queries_used:
                        for i, query in enumerate(result.queries_used[:2], 1):
                            query_syntax = Syntax(
                                query.strip(),
                                "sparql",
                                theme="monokai",
                                line_numbers=False,
                                word_wrap=True
                            )
                            self.console.print(Panel(
                                query_syntax,
                                title=f"🔍 SPARQL Query {i}",
                                border_style="cyan",
                                padding=(0, 1)
                            ))

                    if hasattr(result, 'data_summary') and result.data_summary:
                        self.console.print(Panel(
                            result.data_summary,
                            title="📊 Data Summary",
                            border_style="blue"
                        ))

                elif isinstance(result, dict):
                    # Dictionary result
                    if "error" in result:
                        self.console.print(Panel(
                            f"[red]❌ Error:[/red] {result['error']}",
                            title="Error",
                            border_style="red"
                        ))
                        if "suggestions" in result:
                            suggestions = "\n".join([f"• {s}" for s in result["suggestions"]])
                            self.console.print(Panel(
                                suggestions,
                                title="💡 Suggestions",
                                border_style="yellow"
                            ))
                    else:
                        # Display successful result
                        response_text = result.get("response", str(result))
                        self.console.print(Panel(
                            response_text,
                            title="💬 Answer",
                            border_style="green",
                            padding=(1, 2)
                        ))

                        # Show additional info if available
                        if "data_summary" in result:
                            self.console.print(Panel(
                                result["data_summary"],
                                title="📊 Data Summary",
                                border_style="blue"
                            ))
                else:
                    # String or other result
                    self.console.print(Panel(
                        str(result),
                        title="💬 Answer",
                        border_style="green",
                        padding=(1, 2)
                    ))

            except Exception as e:
                progress.update(task, description="❌ Query failed!")
                self.console.print(f"[red]❌ Error processing query: {e}[/red]")
                import traceback
                self.console.print(f"[dim]{traceback.format_exc()}[/dim]")
    
    def show_help(self):
        """Display help information."""
        help_text = """**Available Commands:**

**Navigation:**
• `select` or `s` - Select a different use case
• `status` or `info` - Show current system status
• `capabilities` or `cap` - Show agent capabilities
• `explore` or `exp` - Explore the loaded data
• `help` or `?` - Show this help message
• `quit`, `exit`, or `q` - Exit the application

**Querying:**
• Simply type your question in natural language
• Examples:
  - "How many addresses are there?"
  - "What cities are represented in the data?"
  - "Show me examples of buildings"
  - "What countries are in the dataset?"

**Tips:**
• Use specific, clear questions for better results
• The enhanced agent understands the data structure
• Check capabilities and explore data for insights
"""
        
        self.console.print(Panel(
            Markdown(help_text),
            title="❓ Help & Commands",
            border_style="blue",
            padding=(1, 2)
        ))
    
    async def interactive_session(self):
        """Run the interactive session."""
        if not self.current_use_case:
            self.console.print("[yellow]Please select a use case first[/yellow]")
            return
        
        self.console.print(f"\n[green]🎯 Active Use Case: {self.current_use_case['name']}[/green]")
        self.console.print("[dim]Type 'help' for available commands, or ask a question directly[/dim]")
        
        while True:
            try:
                question = Prompt.ask(
                    f"\n[bold cyan]Query ({self.current_use_case['name'][:20]})[/bold cyan]",
                    console=self.console
                )
                
                if not question.strip():
                    continue
                
                question_lower = question.lower().strip()
                
                # Handle commands
                if question_lower in ['quit', 'exit', 'q']:
                    self.console.print("[yellow]Goodbye! 👋[/yellow]")
                    break
                
                elif question_lower in ['select', 's']:
                    if await self.select_use_case():
                        self.console.print(f"[green]Switched to: {self.current_use_case['name']}[/green]")
                    continue
                
                elif question_lower in ['status', 'info']:
                    self.print_status()
                    continue
                
                elif question_lower in ['capabilities', 'cap']:
                    await self.show_capabilities()
                    continue
                
                elif question_lower in ['explore', 'exp']:
                    await self.explore_data()
                    continue
                
                elif question_lower in ['help', '?']:
                    self.show_help()
                    continue
                
                # Handle natural language questions
                await self.ask_question(question)
                
            except KeyboardInterrupt:
                self.console.print("\n[yellow]Use 'quit' to exit gracefully[/yellow]")
                continue
            except Exception as e:
                self.console.print(f"[red]❌ Unexpected error: {e}[/red]")
                continue
    
    async def run(self):
        """Main demo entry point."""
        self.print_header()
        
        # Select initial use case
        if not await self.select_use_case():
            self.console.print("[yellow]No use case selected. Exiting.[/yellow]")
            return
        
        # Show initial capabilities
        await self.show_capabilities()
        
        # Start interactive session
        await self.interactive_session()


async def main():
    """Run the CLI demo."""
    demo = CLIDemo()
    await demo.run()


if __name__ == "__main__":
    asyncio.run(main())
