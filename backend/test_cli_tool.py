#!/usr/bin/env python3
"""
Test script for the RDF Agent CLI Tool.
Tests various CLI functionalities and API integration.
"""

import asyncio
import os
import sys
import time
from pathlib import Path

# Add the backend directory to the Python path
sys.path.insert(0, str(Path(__file__).parent))

from cli_tool import APIClient, RDFAgentCLI, UseCase
from rich.console import Console

console = Console()


async def test_api_client():
    """Test the API client functionality."""
    console.print("🧪 Testing API Client...")
    
    async with APIClient() as client:
        # Test health check
        console.print("  📡 Testing health check...")
        is_healthy = await client.health_check()
        if is_healthy:
            console.print("  ✅ Health check passed")
        else:
            console.print("  ❌ Health check failed - API may not be running")
            return False
        
        # Test repositories
        console.print("  📚 Testing repository listing...")
        repos = await client.get_repositories()
        console.print(f"  📊 Found {len(repos)} repositories: {repos}")
        
        # Test repository sizes
        if repos:
            console.print("  📏 Testing repository size queries...")
            for repo in repos[:2]:  # Test first 2 repos
                size = await client.get_repository_size(repo)
                console.print(f"    {repo}: {size} triples" if size else f"    {repo}: size unknown")
        
        # Test session creation
        console.print("  🎯 Testing session creation...")
        session_id = await client.create_session({"test": "cli_test"})
        if session_id:
            console.print(f"  ✅ Session created: {session_id[:8]}...")
            
            # Test session retrieval
            session_data = await client.get_session(session_id)
            if session_data:
                console.print("  ✅ Session retrieved successfully")
            else:
                console.print("  ❌ Failed to retrieve session")
        else:
            console.print("  ❌ Failed to create session")
        
        # Test query examples
        console.print("  💡 Testing query examples...")
        examples = await client.get_query_examples()
        nl_count = len(examples.get("natural_language_examples", []))
        sparql_count = len(examples.get("sparql_examples", []))
        console.print(f"  📝 Found {nl_count} NL examples, {sparql_count} SPARQL examples")
        
        # Test system status
        console.print("  🔍 Testing system status...")
        status = await client.get_system_status()
        overall_status = status.get("status", "unknown")
        services_count = len(status.get("services", {}))
        console.print(f"  📊 System status: {overall_status}, {services_count} services")
        
        console.print("✅ API Client tests completed")
        return True


async def test_cli_components():
    """Test CLI components without full interaction."""
    console.print("\n🧪 Testing CLI Components...")
    
    cli = RDFAgentCLI()
    
    # Test header printing
    console.print("  🎨 Testing header display...")
    cli.print_header()
    
    # Test API connection check
    console.print("  📡 Testing API connection check...")
    connection_ok = await cli.check_api_connection()
    
    if connection_ok:
        console.print("  ✅ API connection test passed")
        
        # Test use case loading
        console.print("  📚 Testing use case loading...")
        async with APIClient() as client:
            cli.api_client = client
            loaded = await cli.load_use_cases()
            
            if loaded:
                console.print(f"  ✅ Loaded {len(cli.use_cases)} use cases")
                
                # Test use case display
                console.print("  🎯 Testing use case display...")
                cli.display_use_cases()
                
                # Test status display
                console.print("  📊 Testing status display...")
                cli.print_status()
                
                # Test help display
                console.print("  ❓ Testing help display...")
                cli.show_help()
                
                # Test query examples display
                console.print("  💡 Testing query examples display...")
                await cli.display_query_examples()
                
            else:
                console.print("  ❌ Failed to load use cases")
    else:
        console.print("  ❌ API connection test failed")
    
    console.print("✅ CLI Components tests completed")


async def test_mock_query():
    """Test query functionality with mock data."""
    console.print("\n🧪 Testing Query Functionality...")
    
    async with APIClient() as client:
        # Check if API is available
        if not await client.health_check():
            console.print("  ⚠️ API not available, skipping query tests")
            return
        
        # Get repositories
        repos = await client.get_repositories()
        if not repos:
            console.print("  ⚠️ No repositories available, skipping query tests")
            return
        
        # Create session
        session_id = await client.create_session({
            "test_mode": True,
            "repository": repos[0]
        })
        
        if not session_id:
            console.print("  ❌ Failed to create session for query test")
            return
        
        console.print(f"  🎯 Testing with repository: {repos[0]}")
        
        # Test natural language query
        console.print("  💬 Testing natural language query...")
        test_questions = [
            "How many entities are there?",
            "What types of data do we have?",
            "Show me some examples"
        ]
        
        for question in test_questions:
            console.print(f"    ❓ Query: {question}")
            response = await client.query(question, session_id, "natural_language")
            
            if response:
                response_text = response.get('response_text', 'No response')
                console.print(f"    ✅ Response: {response_text[:100]}...")
                
                if response.get('sparql_query'):
                    console.print("    🔍 SPARQL query generated")
                
                results = response.get('results', [])
                if results:
                    console.print(f"    📊 {len(results)} results returned")
            else:
                console.print("    ❌ Query failed")
            
            # Small delay between queries
            await asyncio.sleep(1)
        
        # Test SPARQL query
        console.print("  🔍 Testing SPARQL query...")
        sparql_query = "SELECT ?s ?p ?o WHERE { ?s ?p ?o } LIMIT 5"
        response = await client.query(sparql_query, session_id, "sparql")
        
        if response:
            console.print("    ✅ SPARQL query executed successfully")
            results = response.get('results', [])
            console.print(f"    📊 {len(results)} results returned")
        else:
            console.print("    ❌ SPARQL query failed")
    
    console.print("✅ Query functionality tests completed")


def test_cli_commands():
    """Test CLI commands (non-interactive)."""
    console.print("\n🧪 Testing CLI Commands...")
    
    # Test command imports
    try:
        from cli_tool import app
        console.print("  ✅ CLI commands imported successfully")
    except ImportError as e:
        console.print(f"  ❌ Failed to import CLI commands: {e}")
        return
    
    # Test that commands are registered
    commands = app.registered_commands
    console.print(f"  📝 Found {len(commands)} registered commands:")
    if hasattr(commands, 'items'):
        for cmd_name, cmd_info in commands.items():
            console.print(f"    • {cmd_name}: {cmd_info.help or 'No description'}")
    else:
        # Handle case where commands is a list
        for i, cmd in enumerate(commands):
            cmd_name = getattr(cmd, 'name', f'command_{i}')
            cmd_help = getattr(cmd, 'help', 'No description')
            console.print(f"    • {cmd_name}: {cmd_help}")
    
    console.print("✅ CLI Commands tests completed")


async def test_error_handling():
    """Test error handling scenarios."""
    console.print("\n🧪 Testing Error Handling...")
    
    # Test with invalid API URL
    console.print("  🚫 Testing invalid API URL...")
    async with APIClient("http://invalid-url:9999") as client:
        is_healthy = await client.health_check()
        if not is_healthy:
            console.print("  ✅ Correctly handled invalid API URL")
        else:
            console.print("  ❌ Should have failed with invalid URL")
    
    # Test with valid API but invalid endpoints
    async with APIClient() as client:
        if await client.health_check():
            console.print("  🔍 Testing invalid repository...")
            size = await client.get_repository_size("non-existent-repo")
            if size is None:
                console.print("  ✅ Correctly handled invalid repository")
            else:
                console.print("  ❌ Should have returned None for invalid repo")
            
            console.print("  🔍 Testing invalid session...")
            session_data = await client.get_session("invalid-session-id")
            if session_data is None:
                console.print("  ✅ Correctly handled invalid session")
            else:
                console.print("  ❌ Should have returned None for invalid session")
    
    console.print("✅ Error handling tests completed")


async def main():
    """Run all tests."""
    console.print("🚀 Starting RDF Agent CLI Tool Tests\n")
    
    start_time = time.time()
    
    try:
        # Test API client
        api_ok = await test_api_client()
        
        # Test CLI components
        await test_cli_components()
        
        # Test CLI commands
        test_cli_commands()
        
        # Test error handling
        await test_error_handling()
        
        # Test query functionality if API is available
        if api_ok:
            await test_mock_query()
        
        elapsed_time = time.time() - start_time
        console.print(f"\n✅ All tests completed in {elapsed_time:.2f}s!")
        
        console.print("\n📋 Test Summary:")
        console.print("  ✅ API Client functionality")
        console.print("  ✅ CLI Components")
        console.print("  ✅ Command registration")
        console.print("  ✅ Error handling")
        if api_ok:
            console.print("  ✅ Query functionality")
        else:
            console.print("  ⚠️ Query functionality (API not available)")
        
        console.print("\n🎉 CLI Tool is ready for use!")
        console.print("\nTo start the interactive CLI:")
        console.print("  uv run python cli_tool.py")
        console.print("\nTo see all available commands:")
        console.print("  uv run python cli_tool.py --help")
        
    except Exception as e:
        console.print(f"\n❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
