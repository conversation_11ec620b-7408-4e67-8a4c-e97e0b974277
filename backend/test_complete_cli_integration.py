#!/usr/bin/env python3
"""
Complete integration test for the CLI tool with GraphDB repositories.
Tests both command-line mode and interactive mode functionality.
"""

import asyncio
import subprocess
import time
import json
import httpx
from typing import Dict, Any

class CLIIntegrationTester:
    def __init__(self):
        self.api_base_url = "http://localhost:8001"
        self.test_results = []
    
    async def check_api_health(self) -> bool:
        """Check if the API server is running."""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.api_base_url}/health")
                return response.status_code == 200
        except Exception as e:
            print(f"❌ API health check failed: {e}")
            return False
    
    async def get_repositories(self) -> Dict[str, Any]:
        """Get available repositories from the API."""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.api_base_url}/api/v1/admin/repositories")
                if response.status_code == 200:
                    return response.json()
                else:
                    print(f"❌ Failed to get repositories: {response.status_code}")
                    return {}
        except Exception as e:
            print(f"❌ Error getting repositories: {e}")
            return {}
    
    def run_cli_command(self, command: str, timeout: int = 30) -> Dict[str, Any]:
        """Run a CLI command and return the result."""
        try:
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=timeout,
                cwd="."
            )
            
            return {
                "success": result.returncode == 0,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "returncode": result.returncode
            }
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "stdout": "",
                "stderr": "Command timed out",
                "returncode": -1
            }
        except Exception as e:
            return {
                "success": False,
                "stdout": "",
                "stderr": str(e),
                "returncode": -1
            }
    
    def test_cli_help(self):
        """Test CLI help command."""
        print("🧪 Testing CLI help command...")
        result = self.run_cli_command("uv run python cli_tool.py --help")
        
        success = result["success"] and "RDF Agent CLI Tool" in result["stdout"]
        self.test_results.append({
            "test": "CLI Help",
            "success": success,
            "details": result["stdout"][:200] if success else result["stderr"]
        })
        
        if success:
            print("✅ CLI help command works")
        else:
            print(f"❌ CLI help command failed: {result['stderr']}")
    
    def test_list_usecases(self):
        """Test listing use cases."""
        print("🧪 Testing list use cases...")
        result = self.run_cli_command("uv run python cli_tool.py list-usecases")
        
        success = result["success"] and "PortfolioExample" in result["stdout"]
        self.test_results.append({
            "test": "List Use Cases",
            "success": success,
            "details": result["stdout"][:200] if success else result["stderr"]
        })
        
        if success:
            print("✅ List use cases works")
        else:
            print(f"❌ List use cases failed: {result['stderr']}")
    
    def test_direct_query(self):
        """Test direct query command."""
        print("🧪 Testing direct query...")
        result = self.run_cli_command(
            'uv run python cli_tool.py query "How many triples are there?" --use-case PortfolioExample',
            timeout=45
        )
        
        success = result["success"] and ("70" in result["stdout"] or "triples" in result["stdout"])
        self.test_results.append({
            "test": "Direct Query",
            "success": success,
            "details": result["stdout"][:300] if success else result["stderr"]
        })
        
        if success:
            print("✅ Direct query works")
        else:
            print(f"❌ Direct query failed: {result['stderr']}")
    
    def test_interactive_mode(self):
        """Test interactive mode with automated input."""
        print("🧪 Testing interactive mode...")

        # Create input for interactive mode - simplified to avoid timeout
        interactive_input = "1\\nquit\\n"

        result = self.run_cli_command(
            f'printf "{interactive_input}" | uv run python cli_tool.py',
            timeout=30
        )

        success = (result["success"] and
                  "PortfolioExample" in result["stdout"] and
                  "Selected use case" in result["stdout"])

        self.test_results.append({
            "test": "Interactive Mode",
            "success": success,
            "details": result["stdout"][:400] if success else result["stderr"]
        })

        if success:
            print("✅ Interactive mode works")
        else:
            print(f"❌ Interactive mode failed: {result['stderr']}")

    def test_sparql_query(self):
        """Test SPARQL query functionality."""
        print("🧪 Testing SPARQL query...")
        result = self.run_cli_command(
            'uv run python cli_tool.py query "SELECT ?s ?p ?o WHERE { ?s ?p ?o } LIMIT 3" --use-case PortfolioExample --sparql',
            timeout=45
        )

        success = result["success"] and ("executed" in result["stdout"].lower() or "result" in result["stdout"].lower())
        self.test_results.append({
            "test": "SPARQL Query",
            "success": success,
            "details": result["stdout"][:300] if success else result["stderr"]
        })

        if success:
            print("✅ SPARQL query works")
        else:
            print(f"❌ SPARQL query failed: {result['stderr']}")
    
    async def run_all_tests(self):
        """Run all integration tests."""
        print("🚀 Starting CLI Integration Tests")
        print("=" * 50)
        
        # Check API health first
        print("🔍 Checking API health...")
        if not await self.check_api_health():
            print("❌ API server is not running. Please start it first.")
            return
        print("✅ API server is running")
        
        # Check repositories
        print("📚 Checking repositories...")
        repos = await self.get_repositories()
        if not repos or not repos.get("repositories"):
            print("❌ No repositories found")
            return
        print(f"✅ Found {len(repos['repositories'])} repositories")
        
        # Run CLI tests
        print("\n🧪 Running CLI Tests...")
        print("-" * 30)
        
        self.test_cli_help()
        self.test_list_usecases()
        self.test_direct_query()
        self.test_interactive_mode()
        self.test_sparql_query()
        
        # Print results
        print("\n📊 Test Results Summary")
        print("=" * 50)
        
        passed = 0
        total = len(self.test_results)
        
        for result in self.test_results:
            status = "✅ PASS" if result["success"] else "❌ FAIL"
            print(f"{status} - {result['test']}")
            if result["success"]:
                passed += 1
            else:
                print(f"   Details: {result['details']}")
        
        print(f"\n🎯 Overall Result: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! CLI tool is working perfectly!")
        else:
            print(f"⚠️  {total - passed} tests failed. Please check the details above.")

async def main():
    """Main test runner."""
    tester = CLIIntegrationTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
