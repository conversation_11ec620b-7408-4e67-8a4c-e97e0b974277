#!/usr/bin/env python3
"""
Demo CLI for GraphDB-based use cases.
Shows the CLI functionality with actual GraphDB repositories instead of local files.
"""

import asyncio
import os
import sys
from pathlib import Path
from typing import Dict, List, Optional, Any

# Add the backend directory to the Python path
sys.path.insert(0, str(Path(__file__).parent))

# Rich imports for beautiful CLI
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.prompt import Prompt, Confirm, IntPrompt
from rich.text import Text
from rich.table import Table
from rich.markdown import Markdown
from rich import box
from rich.align import Align
from rich.syntax import Syntax

# Import dependencies and GraphDB client
from core.dependencies import get_dependencies
from infrastructure.graphdb_client import GraphDBClient

console = Console()


class GraphDBUseCaseManager:
    """Use case manager that works with GraphDB repositories."""
    
    def __init__(self, graphdb_client: GraphDBClient):
        self.graphdb_client = graphdb_client
    
    async def get_available_repositories(self) -> List[Dict[str, Any]]:
        """Get list of available GraphDB repositories."""
        try:
            repositories = await self.graphdb_client.get_repositories()
            
            use_cases = []
            for repo in repositories:
                # Handle different repository response formats
                if isinstance(repo, str):
                    repo_name = repo
                elif isinstance(repo, dict):
                    repo_name = repo.get("id", repo.get("name", str(repo)))
                else:
                    repo_name = str(repo)
                
                # Get repository size
                try:
                    size = await self.graphdb_client.get_repository_size(repo_name)
                except Exception:
                    size = None
                
                use_case = {
                    "name": repo_name,
                    "repository": repo_name,
                    "description": f"GraphDB repository: {repo_name}",
                    "triple_count": size,
                    "status": "available" if size is not None else "unknown"
                }
                use_cases.append(use_case)
            
            return use_cases
            
        except Exception as e:
            console.print(f"[red]Error getting repositories: {e}[/red]")
            return []


class GraphDBCLIDemo:
    """CLI demonstration class for GraphDB repositories."""
    
    def __init__(self):
        self.console = Console()
        self.use_case_manager: Optional[GraphDBUseCaseManager] = None
        self.graphdb_client: Optional[GraphDBClient] = None
        self.current_repository: Optional[str] = None
        self.current_use_case: Optional[Dict[str, Any]] = None
    
    def print_header(self):
        """Print application header."""
        header_text = """
🚀 RDF Agent CLI Tool - GraphDB Demo
Interactive Use Case Querying with GraphDB Triple Store
        """
        
        header_panel = Panel(
            Align.center(header_text),
            border_style="bright_blue",
            box=box.DOUBLE_EDGE,
            padding=(1, 2)
        )
        
        self.console.print(header_panel)
        self.console.print()
    
    async def initialize_graphdb(self) -> bool:
        """Initialize GraphDB connection."""
        try:
            deps = await get_dependencies()
            self.graphdb_client = deps.graphdb_client
            self.use_case_manager = GraphDBUseCaseManager(self.graphdb_client)
            
            # Test connection
            is_healthy = await self.graphdb_client.health_check()
            if is_healthy:
                console.print("✅ Connected to GraphDB")
                return True
            else:
                console.print("❌ GraphDB health check failed")
                return False
                
        except Exception as e:
            console.print(f"❌ Failed to initialize GraphDB: {e}")
            return False
    
    def display_repositories(self, repositories: List[Dict[str, Any]]):
        """Display available repositories."""
        table = Table(show_header=True, header_style="bold magenta", box=box.ROUNDED)
        table.add_column("#", style="dim", width=3)
        table.add_column("Repository", style="cyan", min_width=25)
        table.add_column("Description", style="blue", min_width=30)
        table.add_column("Triples", style="green", justify="right")
        table.add_column("Status", style="yellow")
        
        for i, repo in enumerate(repositories, 1):
            triple_count = f"{repo['triple_count']:,}" if repo['triple_count'] else "Unknown"
            status = repo['status']
            status_style = "green" if status == "available" else "yellow"
            
            table.add_row(
                str(i),
                repo["name"],
                repo["description"],
                triple_count,
                f"[{status_style}]{status.title()}[/{status_style}]"
            )
        
        panel = Panel(
            table,
            title="🎯 Available GraphDB Repositories",
            border_style="blue",
            padding=(1, 2)
        )
        
        self.console.print(panel)
    
    async def select_repository(self) -> bool:
        """Allow user to select a GraphDB repository."""
        repositories = await self.use_case_manager.get_available_repositories()
        
        if not repositories:
            self.console.print("[red]❌ No GraphDB repositories available[/red]")
            return False
        
        self.display_repositories(repositories)
        
        try:
            choice = IntPrompt.ask(
                "\n[cyan]Select a repository (number)[/cyan]",
                default=1,
                show_default=True,
                console=self.console
            )
            
            if 1 <= choice <= len(repositories):
                selected_repo = repositories[choice - 1]
                self.current_repository = selected_repo["repository"]
                self.current_use_case = selected_repo
                
                self.console.print(f"[green]✅ Selected repository: {selected_repo['name']}[/green]")
                return True
            else:
                self.console.print("[red]Invalid selection[/red]")
                return False
                
        except (ValueError, KeyboardInterrupt):
            self.console.print("[yellow]Selection cancelled[/yellow]")
            return False
    
    def print_status(self):
        """Print current system status."""
        status_table = Table(show_header=False, box=box.MINIMAL)
        status_table.add_column("Item", style="cyan")
        status_table.add_column("Value", style="green")
        
        if self.current_use_case:
            status_table.add_row("🎯 Active Repository", self.current_use_case["name"])
            status_table.add_row("📊 Repository Type", "GraphDB")
            if self.current_use_case["triple_count"]:
                status_table.add_row("🔢 Triple Count", f"{self.current_use_case['triple_count']:,}")
            status_table.add_row("🤖 Status", self.current_use_case["status"].title())
        else:
            status_table.add_row("🎯 Active Repository", "[yellow]None selected[/yellow]")
        
        status_table.add_row("🗄️ Triple Store", "GraphDB")
        
        status_panel = Panel(
            status_table,
            title="📊 System Status",
            border_style="green" if self.current_repository else "yellow",
            padding=(0, 1)
        )
        
        self.console.print(status_panel)
    
    async def execute_sparql_query(self, query: str):
        """Execute a SPARQL query against the current repository."""
        if not self.current_repository:
            self.console.print("[red]❌ No repository selected[/red]")
            return

        try:
            # Execute query using GraphDB client
            result_data = await self.graphdb_client.execute_sparql_query(
                query,
                repository=self.current_repository
            )

            # Handle different result formats
            if isinstance(result_data, dict):
                # Check if it's a SPARQL JSON result format
                if "results" in result_data and "bindings" in result_data["results"]:
                    bindings = result_data["results"]["bindings"]
                    variables = result_data.get("head", {}).get("vars", [])

                    if bindings:
                        # Create table for SELECT results
                        table = Table(show_header=True, header_style="bold magenta", box=box.ROUNDED)

                        # Add columns
                        for var in variables:
                            table.add_column(var, overflow="fold", max_width=30)

                        # Add rows (limit to first 10)
                        for binding in bindings[:10]:
                            row_values = []
                            for var in variables:
                                if var in binding:
                                    value = binding[var].get("value", "")
                                    if len(value) > 50:
                                        value = value[:47] + "..."
                                    row_values.append(value)
                                else:
                                    row_values.append("")
                            table.add_row(*row_values)

                        if len(bindings) > 10:
                            table.add_row(*[f"... (+{len(bindings) - 10} more)" if i == 0 else ""
                                           for i in range(len(variables))])

                        self.console.print(Panel(
                            table,
                            title=f"📋 Query Results ({len(bindings)} total)",
                            border_style="green"
                        ))
                    else:
                        self.console.print("[yellow]Query executed successfully but returned no results[/yellow]")

                elif "boolean" in result_data:
                    # ASK query result
                    result_value = result_data["boolean"]
                    self.console.print(Panel(
                        f"[bold green]Result: {result_value}[/bold green]",
                        title="📋 ASK Query Result",
                        border_style="green"
                    ))

                else:
                    # Other result format - display as JSON
                    import json
                    result_text = json.dumps(result_data, indent=2)
                    self.console.print(Panel(
                        result_text,
                        title="📋 Query Results",
                        border_style="green"
                    ))

            elif isinstance(result_data, list):
                # List of results
                if result_data:
                    if isinstance(result_data[0], dict):
                        # Create table for list of dictionaries
                        table = Table(show_header=True, header_style="bold magenta", box=box.ROUNDED)

                        # Add columns based on first result
                        for key in result_data[0].keys():
                            table.add_column(key, overflow="fold", max_width=30)

                        # Add rows (limit to first 10)
                        for result in result_data[:10]:
                            row_values = []
                            for key in result_data[0].keys():
                                value = str(result.get(key, ""))
                                if len(value) > 50:
                                    value = value[:47] + "..."
                                row_values.append(value)
                            table.add_row(*row_values)

                        if len(result_data) > 10:
                            table.add_row(*[f"... (+{len(result_data) - 10} more)" if i == 0 else ""
                                           for i in range(len(result_data[0].keys()))])

                        self.console.print(Panel(
                            table,
                            title=f"📋 Query Results ({len(result_data)} total)",
                            border_style="green"
                        ))
                    else:
                        # Simple list display
                        results_text = "\n".join([str(result) for result in result_data[:10]])
                        if len(result_data) > 10:
                            results_text += f"\n... and {len(result_data) - 10} more results"

                        self.console.print(Panel(
                            results_text,
                            title=f"📋 Query Results ({len(result_data)} total)",
                            border_style="green"
                        ))
                else:
                    self.console.print("[yellow]Query executed successfully but returned no results[/yellow]")

            else:
                # Other result type
                self.console.print(Panel(
                    str(result_data),
                    title="📋 Query Results",
                    border_style="green"
                ))

        except Exception as e:
            self.console.print(f"[red]❌ Query failed: {e}[/red]")
    
    async def ask_question(self, question: str):
        """Process a user question (for demo, we'll show a sample SPARQL query)."""
        if not self.current_repository:
            self.console.print("[red]❌ No repository selected[/red]")
            return
        
        # Display the question
        self.console.print(Panel(
            f"[bold cyan]Question:[/bold cyan] {question}",
            title="❓ User Query",
            border_style="blue",
            padding=(0, 1)
        ))
        
        # For demo purposes, show some sample queries based on common questions
        sample_queries = {
            "how many": "SELECT (COUNT(*) as ?count) WHERE { ?s ?p ?o }",
            "what types": "SELECT DISTINCT ?type WHERE { ?s a ?type }",
            "show me": "SELECT ?s ?p ?o WHERE { ?s ?p ?o } LIMIT 10",
            "list": "SELECT DISTINCT ?s WHERE { ?s ?p ?o } LIMIT 10"
        }
        
        # Find matching query
        sparql_query = None
        for keyword, query in sample_queries.items():
            if keyword in question.lower():
                sparql_query = query
                break
        
        if not sparql_query:
            sparql_query = "SELECT ?s ?p ?o WHERE { ?s ?p ?o } LIMIT 5"
        
        # Show the generated SPARQL
        query_syntax = Syntax(
            sparql_query,
            "sparql",
            theme="monokai",
            line_numbers=False,
            word_wrap=True
        )
        
        self.console.print(Panel(
            query_syntax,
            title="🔍 Generated SPARQL Query",
            border_style="cyan",
            padding=(0, 1)
        ))
        
        # Execute the query
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console,
            transient=True
        ) as progress:
            task = progress.add_task("🤖 Executing query...", total=None)
            await self.execute_sparql_query(sparql_query)
            progress.update(task, description="✅ Query completed!")
    
    def show_help(self):
        """Display help information."""
        help_text = """**Available Commands:**

**Navigation:**
• `select` or `s` - Select a different repository
• `status` or `info` - Show current system status
• `help` or `?` - Show this help message
• `quit`, `exit`, or `q` - Exit the application

**Querying:**
• Simply type your question in natural language
• Prefix with `sparql:` to execute raw SPARQL queries
• Examples:
  - "How many triples are there?"
  - "What types of entities exist?"
  - "Show me some data"
  - "sparql: SELECT ?s ?p ?o WHERE { ?s ?p ?o } LIMIT 10"

**Tips:**
• This demo works with actual GraphDB repositories
• Questions are converted to sample SPARQL queries
• Use the full CLI tool for AI-powered query generation
"""
        
        self.console.print(Panel(
            Markdown(help_text),
            title="❓ Help & Commands",
            border_style="blue",
            padding=(1, 2)
        ))
    
    async def interactive_session(self):
        """Run the interactive session."""
        if not self.current_repository:
            self.console.print("[yellow]Please select a repository first[/yellow]")
            return
        
        self.console.print(f"\n[green]🎯 Active Repository: {self.current_use_case['name']}[/green]")
        self.console.print("[dim]Type 'help' for available commands, or ask a question directly[/dim]")
        
        while True:
            try:
                question = Prompt.ask(
                    f"\n[bold cyan]Query ({self.current_use_case['name'][:20]})[/bold cyan]",
                    console=self.console
                )
                
                if not question.strip():
                    continue
                
                question_lower = question.lower().strip()
                
                # Handle commands
                if question_lower in ['quit', 'exit', 'q']:
                    self.console.print("[yellow]Goodbye! 👋[/yellow]")
                    break
                
                elif question_lower in ['select', 's']:
                    if await self.select_repository():
                        self.console.print(f"[green]Switched to: {self.current_use_case['name']}[/green]")
                    continue
                
                elif question_lower in ['status', 'info']:
                    self.print_status()
                    continue
                
                elif question_lower in ['help', '?']:
                    self.show_help()
                    continue
                
                # Handle SPARQL queries
                elif question.lower().startswith('sparql:'):
                    sparql_query = question[7:].strip()
                    if sparql_query:
                        query_syntax = Syntax(
                            sparql_query,
                            "sparql",
                            theme="monokai",
                            line_numbers=False,
                            word_wrap=True
                        )
                        
                        self.console.print(Panel(
                            query_syntax,
                            title="🔍 SPARQL Query",
                            border_style="cyan",
                            padding=(0, 1)
                        ))
                        
                        await self.execute_sparql_query(sparql_query)
                    continue
                
                # Handle natural language questions
                await self.ask_question(question)
                
            except KeyboardInterrupt:
                self.console.print("\n[yellow]Use 'quit' to exit gracefully[/yellow]")
                continue
            except Exception as e:
                self.console.print(f"[red]❌ Unexpected error: {e}[/red]")
                continue
    
    async def run(self):
        """Main demo entry point."""
        self.print_header()
        
        # Initialize GraphDB connection
        if not await self.initialize_graphdb():
            self.console.print("[red]Cannot proceed without GraphDB connection[/red]")
            return
        
        # Select repository
        if not await self.select_repository():
            self.console.print("[yellow]No repository selected. Exiting.[/yellow]")
            return
        
        # Show status
        self.print_status()
        
        # Start interactive session
        await self.interactive_session()


async def main():
    """Run the GraphDB CLI demo."""
    demo = GraphDBCLIDemo()
    await demo.run()


if __name__ == "__main__":
    asyncio.run(main())
