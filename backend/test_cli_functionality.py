#!/usr/bin/env python3
"""
Test script to demonstrate CLI functionality without interactive prompts.
"""

import asyncio
import sys
from pathlib import Path

# Add the backend directory to the Python path
sys.path.insert(0, str(Path(__file__).parent))

from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich import box
from rich.syntax import Syntax

from agents.rdf_query_agent import RDFQueryAgent
from core.dependencies import get_dependencies

console = Console()


async def test_cli_functionality():
    """Test the CLI functionality programmatically."""
    
    console.print("🧪 Testing CLI Functionality\n")
    
    # Test 1: Initialize RDF Agent
    console.print("1️⃣ Initializing RDF Agent...")
    try:
        deps = await get_dependencies()
        agent = RDFQueryAgent(deps)
        console.print("   ✅ RDF Agent initialized successfully")
    except Exception as e:
        console.print(f"   ❌ Failed to initialize RDF Agent: {e}")
        return
    
    # Test 2: Load TTL file
    console.print("\n2️⃣ Loading TTL file...")
    ttl_file = "test_data/example.ttl"
    if not Path(ttl_file).exists():
        console.print(f"   ❌ TTL file not found: {ttl_file}")
        return
    
    try:
        response = await agent.load_ttl_file(ttl_file)
        if response.success:
            console.print(f"   ✅ TTL file loaded: {response.message}")
        else:
            console.print(f"   ❌ Failed to load TTL: {response.message}")
            return
    except Exception as e:
        console.print(f"   ❌ Error loading TTL: {e}")
        return
    
    # Test 3: Get capabilities
    console.print("\n3️⃣ Getting agent capabilities...")
    try:
        capabilities = await agent.get_capabilities()
        
        cap_table = Table(show_header=False, box=box.MINIMAL)
        cap_table.add_column("Property", style="cyan")
        cap_table.add_column("Value", style="green")
        
        cap_table.add_row("TTL Loaded", "✅ Yes" if capabilities.ttl_loaded else "❌ No")
        cap_table.add_row("Total Triples", f"{capabilities.total_triples:,}")
        cap_table.add_row("Available Classes", str(len(capabilities.available_classes)))
        cap_table.add_row("Data Domains", ", ".join(capabilities.data_domains))
        
        console.print(Panel(
            cap_table,
            title="🔍 Agent Capabilities",
            border_style="cyan"
        ))
        
        # Show sample queries
        if capabilities.sample_queries:
            console.print("\n   📝 Sample Queries:")
            for i, query in enumerate(capabilities.sample_queries[:3], 1):
                console.print(f"      {i}. {query}")
        
    except Exception as e:
        console.print(f"   ❌ Error getting capabilities: {e}")
        return
    
    # Test 4: Explore data
    console.print("\n4️⃣ Exploring data...")
    try:
        exploration = await agent.explore_data()
        
        console.print(f"   📊 Summary: {exploration.summary}")
        
        if exploration.key_entities:
            console.print("   🔑 Key Entities:")
            for entity in exploration.key_entities[:3]:
                console.print(f"      • {entity}")
        
        if exploration.suggested_questions:
            console.print("   💡 Suggested Questions:")
            for i, suggestion in enumerate(exploration.suggested_questions[:3], 1):
                console.print(f"      {i}. {suggestion.question}")
        
    except Exception as e:
        console.print(f"   ❌ Error exploring data: {e}")
    
    # Test 5: Process queries
    console.print("\n5️⃣ Processing sample queries...")
    
    test_queries = [
        "How many buildings are there?",
        "How many addresses are there?",
        "What cities are represented in the data?"
    ]
    
    for i, query in enumerate(test_queries, 1):
        console.print(f"\n   Query {i}: {query}")
        
        try:
            result = await agent.process_query(query)
            
            if hasattr(result, 'answer'):
                # FinalAnswer object
                console.print(Panel(
                    result.answer,
                    title=f"💬 Answer {i}",
                    border_style="green",
                    padding=(0, 1)
                ))
                
                if hasattr(result, 'confidence') and result.confidence:
                    console.print(f"      Confidence: {result.confidence:.1%}")
                
                if hasattr(result, 'queries_used') and result.queries_used:
                    for j, sparql_query in enumerate(result.queries_used[:1], 1):
                        query_syntax = Syntax(
                            sparql_query.strip(),
                            "sparql",
                            theme="monokai",
                            line_numbers=False,
                            word_wrap=True
                        )
                        console.print(Panel(
                            query_syntax,
                            title=f"🔍 SPARQL Query {i}.{j}",
                            border_style="cyan",
                            padding=(0, 1)
                        ))
            
            elif isinstance(result, dict):
                if "error" in result:
                    console.print(f"      ❌ Error: {result['error']}")
                else:
                    response_text = result.get("response", str(result))
                    console.print(f"      ✅ Response: {response_text}")
            else:
                console.print(f"      ✅ Response: {str(result)}")
                
        except Exception as e:
            console.print(f"      ❌ Error processing query: {e}")
    
    # Test 6: CLI Commands simulation
    console.print("\n6️⃣ Simulating CLI commands...")
    
    # Simulate status command
    console.print("\n   📊 Status Command:")
    status_table = Table(show_header=False, box=box.MINIMAL)
    status_table.add_column("Item", style="cyan")
    status_table.add_column("Value", style="green")
    
    status_table.add_row("🎯 Active Use Case", "Example Building Data")
    status_table.add_row("📁 Data File", ttl_file)
    status_table.add_row("🔢 Triple Count", f"{capabilities.total_triples:,}")
    status_table.add_row("🤖 Agent Status", "Ready")
    
    console.print(Panel(
        status_table,
        title="📊 System Status",
        border_style="green"
    ))
    
    # Simulate help command
    console.print("\n   ❓ Help Command:")
    help_text = """**Available Commands:**

**Navigation:**
• `select` or `s` - Select a different use case
• `status` or `info` - Show current system status
• `capabilities` or `cap` - Show agent capabilities
• `explore` or `exp` - Explore the loaded data
• `help` or `?` - Show this help message
• `quit`, `exit`, or `q` - Exit the application

**Querying:**
• Simply type your question in natural language
• Examples:
  - "How many addresses are there?"
  - "What cities are represented in the data?"
  - "Show me examples of buildings"
"""
    
    console.print(Panel(
        help_text,
        title="❓ Help & Commands",
        border_style="blue",
        padding=(1, 2)
    ))
    
    console.print("\n✅ CLI functionality test completed successfully!")
    console.print("\n🎉 The CLI tool is working correctly!")
    
    console.print("\n📋 Summary:")
    console.print("  ✅ RDF Agent initialization")
    console.print("  ✅ TTL file loading")
    console.print("  ✅ Capabilities retrieval")
    console.print("  ✅ Data exploration")
    console.print("  ✅ Query processing")
    console.print("  ✅ CLI command simulation")
    
    console.print("\n🚀 To use the interactive CLI:")
    console.print("  uv run python demo_cli_tool.py")
    console.print("\n🔧 To use the full API-integrated CLI:")
    console.print("  uv run python cli_tool.py")


async def main():
    """Main function."""
    await test_cli_functionality()


if __name__ == "__main__":
    asyncio.run(main())
