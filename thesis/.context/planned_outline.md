Bachelor's Thesis: ConversationalBIM
Preliminary Outline
• Chapter 1: Introduction and Problem Statement
o Methodological Approach: Systematic problem analysis with gap identification in
current BIM-LLM integration.
• Chapter 2: Fundamentals and Related Work
o Methodological Approach: Systematic literature review focusing on LBD, LLM-
Graph integration, and HCI metrics.
• Chapter 3: System Design and Architecture
o Methodological Approach: Design Science Research Approach with iterative
prototype development.
• Chapter 4: Implementation
o Methodological Approach: Agile development methodology with continuous
component evaluation.
• Chapter 5: Evaluation and Usability Study
o Methodological Approach: Quantitative KLM-based analysis supplemented by
qualitative user feedback.
• Chapter 6: Results and Discussion
o Methodological Approach: Statistical analysis with interpretation of practical
significance.
• Chapter 7: Conclusion and Outlook
o Methodological Approach: Reflection on hypothesis validation and identification
of future research directions.
