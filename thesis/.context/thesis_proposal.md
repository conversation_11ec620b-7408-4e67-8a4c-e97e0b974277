1. Introduction / Context / Research Topic
   1.1 Problem Statement and Relevance
   The digital transformation in the construction industry has created new possibilities for data
   structuring and management through Building Information Modeling (BIM). This method enables
   detailed digital representations of physical and functional characteristics of buildings—unlike
   traditional construction, which relies heavily on paper-based, disconnected documentation [1].
   Standards such as Industry Foundation Classes (IFC), which structure building data
   hierarchically using entities like projects, buildings, floors, and components, further complement
   this digital shift [2]. Beyond the rigid schema of IFC, Linked Building Data (LBD) theoretically
   offers significant advantages by organizing and linking data in an intuitive, web-based format,
   promising clearer insights, improved coordination, and more informed decision-making. Despite
   this potential, the technology remains underrepresented in practice, likely due to its high
   implementation complexity and the limitation of user-friendly tools. [3, 4].
   Overarching Problem: Modern building documentation is often extremely complex and
   voluminous. Adding to this challenge, information is typically scattered across fragmented
   storage locations, with much of it in unstructured, non-searchable formats such as PDFs. Linked
   Building Data (LBD) promises to address these issues by organizing and connecting data in a
   way that improves accessibility and efficiency.
   Specific Sub-problem: Graph-based user interfaces, such as the neo4j Browser, offer a way to
   explore Linked Building Data (LBD) in practice. However, these tools demand technical
   expertise and rely on complex query languages [5], making the process time-consuming and
   difficult. This underscores the need for more accessible, user-friendly solutions to fully unlock
   LBD’s potential while highlighting the importance of further research into its adoption and
   challenges [6].
   1.2 Research Context and Motivation
   This work is conducted within the framework of the ZIM project portfolioBIM, a collaborative
   research initiative between HTW Dresden University, ekkodale GmbH, and Metabuild GmbH.
   The project focuses on Portfolio Information Management, developing solutions for the
   integration and management of building data. To achieve this, the platform ingests building data
   from Excel files based on the IBPDI standard to generate a graph visualization, which enables
   structured access to individual building components. Within this broader context, this thesis
   specifically addresses the final processing stage of the workflow—making this structured
   building data accessible through chat interfaces.
   The company ekkodale GmbH, a specialist in digital transformation in the construction industry
   with over 25 years of experience in BIM solutions, provides the practical application context for
   developing and evaluating the proposed solution. The collaborative and research-driven nature
   of the PortfolioBIM project ensures that the developed interface is embedded in an open-source
   ecosystem and benefits from interdisciplinary input.
   1.3 Research Gap
   The intersection of Linked Building Data (LBD) and Large Language Model (LLM) technologies
   raises new research questions regarding their integration for comprehensive information
   retrieval. A key question is how to design a system that allows a user to query both a structured
   knowledge graph and its linked, unstructured documents through a single natural language
   interface. This thesis explores a potential solution through a tool-augmented LLM architecture,
   where the model is equipped to programmatically query the graph and semantically search
   related documents. A second critical research question is whether such an interface offers
   measurable improvements in user efficiency, highlighting a gap in the literature concerning the
   quantitative comparison between AI-driven chat interfaces and traditional graph UIs [9, 10].
2. Objectives and Research Interest
   2.1 Primary Research Goals
   • Technical Integration: Development of an LLM-based chat interface for natural
   language interaction with attached building documents through Linked Building Data
   graph structures.
   • Usability Comparison: This research aims to quantitatively demonstrate the efficiency
   gains of the proposed LLM-based chat interface when compared to existing graph user
   interfaces for common building information retrieval tasks. The comparison will focus on
   identifying measurable improvements in user interaction and task completion time [11,
   12].
   2.2 Research Hypotheses
   • Main Hypothesis (H1): The efficiency of accessing and interacting with building
   documentation in a graph is significantly improved through an LLM-based chat interface
   compared to traditional graph UIs, measurable through the Keystroke-Level Model plus
   Reaction time (KLM + R).
   • 3. State of Research
   3.1 Linked Building Data and BIM
   Linked Building Data (LBD) represents an advancement of Building Information Modeling (BIM)
   by employing semantic web technologies to create building data that is both machine-readable
   and interoperable, overcoming the limitations of rigid schemas like Industry Foundation Classes
   (IFC) [4]. Despite its potential to create a unified and queryable web of building information, the
   practical adoption of LBD remains limited. This is largely due to the high complexity associated
   with its implementation and a notable absence of user-friendly interfaces designed for non-
   technical users [1].

3.2 Graph Interaction (Query vs UI vs LLM)
Interacting with graph databases typically involves three main approaches:
• Complex graph queries (e.g., Cypher for Neo4j Browser) are powerful but require
technical expertise, posing a significant barrier for non-technical users [5].
• Graphical user interfaces (GUIs) simplify interaction through visual navigation but can
still be cumbersome for large graphs, demanding manual traversal to find specific
information or documents.
• Large Language Models (LLMs), especially with Retrieval-Augmented Generation
(RAG), offer a promising approach [10, 9]. They enable natural language queries,
abstracting away technical complexity and directly retrieving relevant information from
the graph and associated documents, significantly streamlining information access [6]. 4. Concept
4.1 Technical Integration
4.1.1 User Interface (UI Layer)
• User Input: Accepts natural language queries.
• Chat Display: Presents structured system responses with references.
4.1.2 Processing Layer (LLM & Model Context Protocols)
• Central LLM: Orchestrates query interpretation and response generation.
• Model Context Protocols (MCPs): Modular tools that support the LLM by connecting to
external data sources.
• Knowledge Graph MCP – Create Graph Query: Converts natural language into
Cypher queries, queries the graph database, and returns relevant nodes/relationships
[6].
• RAG MCP – Get Best Matching Chunks: Performs semantic search on attached PDFs
using embeddings and returns relevant text [10].
• Knowledge Graph MCP – Assign Document to Nodes: Adds document references to
graph nodes and confirms indexing.
4.1.3 Data Layer
• Graph Database: Models for building entities, systems, and relationships.
• Document Storage: Stores building documentation using scalable object storage (e.g.,
S3, MinIO).
4.2 Usability Comparison Methodology
Usability is compared between two approaches to accessing building documentation: a more
traditional graph UI and a natural language interface powered by a large language model
(LLM).To quantify the difference, the Keystroke-Level Model (KLM) will be used to model both
interaction methods by breaking down each task into standardized operators with defined time
values [12]. Standardized scenarios reflecting realistic user goals will serve as the basis for
these models. Performance will be benchmarked using Nielsen’s Response Time Guidelines to
evaluate responsiveness and user experience expectations [13]. Finally, a paired t-test will be
used to statistically compare the predicted interaction times between the traditional graphical
interface and the LLM-based natural language interface, assessing whether the latter offers a
significant improvement in efficiency [11]. 5. Preliminary Outline
• Chapter 1: Introduction and Problem Statement
o Methodological Approach: Systematic problem analysis with gap identification in
current BIM-LLM integration.
• Chapter 2: Fundamentals and Related Work
o Methodological Approach: Systematic literature review focusing on LBD, LLM-
Graph integration, and HCI metrics.
• Chapter 3: System Design and Architecture
o Methodological Approach: Design Science Research Approach with iterative
prototype development.
• Chapter 4: Implementation
o Methodological Approach: Agile development methodology with continuous
component evaluation.
• Chapter 5: Evaluation and Usability Study
o Methodological Approach: Quantitative KLM-based analysis supplemented by
qualitative user feedback.
• Chapter 6: Results and Discussion
o Methodological Approach: Statistical analysis with interpretation of practical
significance.
• Chapter 7: Conclusion and Outlook
o Methodological Approach: Reflection on hypothesis validation and identification
of future research directions. 6. Timeline
Phase Timefra
me
Activities Milestones
Literature Review Week 1-
2
Systematic research on LBD, LLM, HCI Complete bibliography
Architecture Design Week 3 System design, MCP definition, DB schema Technical specification
Prototype
Development
Week 4-
7
LLM integration, RAG implementation, KG setup Functional prototype
KLM Analysis Week 8 Task definition, operator sequences, time
measurement
KLM comparison data
Documentation Week 9-
12
Thesis writing, revision, finalization Completed bachelor
thesis 7. Personal Motivation
The motivation for this work is deeply rooted in my personal interest in bridging the gap between
advanced technology and practical usability. The idea that natural language interfaces could
revolutionize how we interact with building data, making it more accessible and user-friendly, is
particularly exciting to me.
Moreover, being part of the ZIM project PortfolioBIM presents a unique and personally
motivating opportunity. This project allows me to contribute to the development of open-source
solutions that not only have academic value but also practical applications in the real world. The
prospect of creating something that can be widely used and beneficial beyond the confines of
academia is incredibly inspiring. It aligns with my passion for developing technologies that have
a direct and positive impact on people's daily lives and work environments. 8. References
