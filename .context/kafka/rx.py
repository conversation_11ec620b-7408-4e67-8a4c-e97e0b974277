#!/usr/bin/env python3
import json
from kafka import KafkaConsumer

def main():
    # Kafka configuration
    consumer = KafkaConsumer(
        'usecases.sample_input',
        bootstrap_servers=['localhost:9092'],
        value_deserializer=lambda x: json.loads(x.decode('utf-8')),
        auto_offset_reset='earliest',
        group_id='sample_consumer_group'
    )
    
    print("Listening for messages on topic 'usecases.sample_input'...")
    print("Press Ctrl+C to stop")
    
    try:
        for message in consumer:
            print(f"Received message:")
            print(f"  Topic: {message.topic}")
            print(f"  Partition: {message.partition}")
            print(f"  Offset: {message.offset}")
            print(f"  Data: {json.dumps(message.value, indent=2)}")
            print("-" * 50)
            
    except KeyboardInterrupt:
        print("\nStopping consumer...")
    finally:
        consumer.close()

if __name__ == "__main__":
    main()
