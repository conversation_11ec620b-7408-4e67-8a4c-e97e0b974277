#!/usr/bin/env python3
import json
import os
from kafka import KafkaProducer

def main():
    topic = 'usecases.sample_input'
    
    # Kafka configuration
    producer = KafkaProducer(
        bootstrap_servers=['localhost:9092'],
        value_serializer=lambda x: json.dumps(x).encode('utf-8'),
        max_block_ms=10000,  # Reduce timeout
        request_timeout_ms=10000,
        retries=1
    )
    
    # Load data from sample_input.json
    script_dir = os.path.dirname(__file__)
    json_file = os.path.join(script_dir, 'sample_input.json')
    
    try:
        with open(json_file, 'r') as f:
            data = json.load(f)
        
        topic = 'usecases.sample_input'
        
        print(f"Publishing {len(data)} messages to topic '{topic}'...")
        
        for i, item in enumerate(data):
            # Skip empty items
            if not item:
                continue
                
            producer.send(topic, item)
            print(f"Sent message {i + 1}")
        
        # Ensure all messages are sent
        producer.flush()
        print("All messages sent successfully!")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        producer.close()

if __name__ == "__main__":
    main()
