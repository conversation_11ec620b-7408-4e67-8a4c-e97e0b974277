---
type: "always_apply"
description: "Example description"
---

- Always use uv instead of python.
- uv add <package> to add a library
- uv run <script> to run a python script/module
- NEVER use python <script> or pip install <package>
- Try to always check context7 to get the documentation for what you are trying to do with a library to understand how exactly to do that. Especially for libraries that are not as common.
